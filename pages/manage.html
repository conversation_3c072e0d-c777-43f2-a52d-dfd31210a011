<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>管理中心 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="app-content">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">管理中心</h1>
      <div class="page-actions">
        <button class="btn-icon">
          <i class="fas fa-plus"></i>
        </button>
        <button class="btn-icon">
          <i class="fas fa-cog"></i>
        </button>
      </div>
    </div>

    <!-- 管理功能菜单 -->
    <div class="management-grid mb-16">
      <a href="#" class="management-item" data-module="alerts">
        <div class="management-icon" style="background: var(--danger-red);">
          <i class="fas fa-bell"></i>
        </div>
        <div class="management-info">
          <div class="management-title">告警管理</div>
          <div class="management-desc">告警规则、通知设置</div>
        </div>
        <div class="management-badge">
          <span class="badge badge-danger">3</span>
        </div>
      </a>

      <a href="#" class="management-item" data-module="devices">
        <div class="management-icon" style="background: var(--primary-blue);">
          <i class="fas fa-microchip"></i>
        </div>
        <div class="management-info">
          <div class="management-title">设备管理</div>
          <div class="management-desc">设备台账、状态监控</div>
        </div>
        <div class="management-badge">
          <span class="badge badge-success">85</span>
        </div>
      </a>

      <a href="#" class="management-item" data-module="reports">
        <div class="management-icon" style="background: var(--success-green);">
          <i class="fas fa-file-alt"></i>
        </div>
        <div class="management-info">
          <div class="management-title">报表中心</div>
          <div class="management-desc">报表生成、导出</div>
        </div>
        <div class="management-badge">
          <span class="badge badge-info">12</span>
        </div>
      </a>

      <a href="#" class="management-item" data-module="control">
        <div class="management-icon" style="background: var(--warning-orange);">
          <i class="fas fa-sliders-h"></i>
        </div>
        <div class="management-info">
          <div class="management-title">节能控制</div>
          <div class="management-desc">策略配置、执行监控</div>
        </div>
        <div class="management-badge">
          <span class="badge badge-warning">5</span>
        </div>
      </a>

      <a href="#" class="management-item" data-module="carbon">
        <div class="management-icon" style="background: var(--success-green);">
          <i class="fas fa-leaf"></i>
        </div>
        <div class="management-info">
          <div class="management-title">碳资产管理</div>
          <div class="management-desc">碳排放、碳交易</div>
        </div>
        <div class="management-badge">
          <span class="badge badge-success">2.3t</span>
        </div>
      </a>

      <a href="#" class="management-item" data-module="maintenance">
        <div class="management-icon" style="background: var(--text-secondary);">
          <i class="fas fa-tools"></i>
        </div>
        <div class="management-info">
          <div class="management-title">运维管理</div>
          <div class="management-desc">工单管理、巡检计划</div>
        </div>
        <div class="management-badge">
          <span class="badge badge-warning">7</span>
        </div>
      </a>
    </div>

    <!-- 告警管理详情 -->
    <div class="module-content" id="alerts-content">
      <div class="card mb-16">
        <div class="card-header">
          <h3 class="card-title">活跃告警</h3>
          <div class="alert-filters">
            <button class="filter-btn active" data-level="all">全部</button>
            <button class="filter-btn" data-level="critical">严重</button>
            <button class="filter-btn" data-level="warning">警告</button>
            <button class="filter-btn" data-level="info">提醒</button>
          </div>
        </div>
        <div class="alert-list">
          <div class="alert-item critical">
            <div class="alert-indicator">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="alert-content">
              <div class="alert-title">B栋用电功率超限</div>
              <div class="alert-desc">当前功率856kW，超过阈值800kW</div>
              <div class="alert-meta">
                <span class="alert-time">2分钟前</span>
                <span class="alert-source">电表-B001</span>
              </div>
            </div>
            <div class="alert-actions">
              <button class="btn-action acknowledge">
                <i class="fas fa-check"></i>
              </button>
              <button class="btn-action details">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <div class="alert-item warning">
            <div class="alert-indicator">
              <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="alert-content">
              <div class="alert-title">水表通信异常</div>
              <div class="alert-desc">水表-002连接中断，无法获取数据</div>
              <div class="alert-meta">
                <span class="alert-time">15分钟前</span>
                <span class="alert-source">水表-W002</span>
              </div>
            </div>
            <div class="alert-actions">
              <button class="btn-action acknowledge">
                <i class="fas fa-check"></i>
              </button>
              <button class="btn-action details">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>

          <div class="alert-item info">
            <div class="alert-indicator">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="alert-content">
              <div class="alert-title">设备维护提醒</div>
              <div class="alert-desc">电表-001需要进行定期维护检查</div>
              <div class="alert-meta">
                <span class="alert-time">1小时前</span>
                <span class="alert-source">维护系统</span>
              </div>
            </div>
            <div class="alert-actions">
              <button class="btn-action acknowledge">
                <i class="fas fa-check"></i>
              </button>
              <button class="btn-action details">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="card mb-16">
        <div class="card-header">
          <h3 class="card-title">告警统计</h3>
          <select class="select-small">
            <option>今日</option>
            <option>本周</option>
            <option>本月</option>
          </select>
        </div>
        <div class="alert-stats">
          <div class="stat-card critical">
            <div class="stat-number">3</div>
            <div class="stat-label">严重告警</div>
            <div class="stat-change">+1</div>
          </div>
          <div class="stat-card warning">
            <div class="stat-number">8</div>
            <div class="stat-label">警告告警</div>
            <div class="stat-change">-2</div>
          </div>
          <div class="stat-card info">
            <div class="stat-number">15</div>
            <div class="stat-label">提醒告警</div>
            <div class="stat-change">+5</div>
          </div>
          <div class="stat-card resolved">
            <div class="stat-number">42</div>
            <div class="stat-label">已处理</div>
            <div class="stat-change">+12</div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3 class="card-title">告警规则配置</h3>
          <button class="btn btn-primary btn-small">
            <i class="fas fa-plus"></i>
            <span>新增规则</span>
          </button>
        </div>
        <div class="rule-list">
          <div class="rule-item">
            <div class="rule-info">
              <div class="rule-name">用电功率超限</div>
              <div class="rule-condition">功率 > 800kW</div>
            </div>
            <div class="rule-status">
              <span class="status-indicator active">
                <span class="status-dot status-online"></span>
                <span>启用</span>
              </span>
            </div>
            <div class="rule-actions">
              <button class="btn-icon-small">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn-icon-small">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <div class="rule-item">
            <div class="rule-info">
              <div class="rule-name">设备离线检测</div>
              <div class="rule-condition">通信中断 > 5分钟</div>
            </div>
            <div class="rule-status">
              <span class="status-indicator active">
                <span class="status-dot status-online"></span>
                <span>启用</span>
              </span>
            </div>
            <div class="rule-actions">
              <button class="btn-icon-small">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn-icon-small">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <div class="rule-item">
            <div class="rule-info">
              <div class="rule-name">能耗异常检测</div>
              <div class="rule-condition">用电量变化 > 20%</div>
            </div>
            <div class="rule-status">
              <span class="status-indicator inactive">
                <span class="status-dot status-offline"></span>
                <span>禁用</span>
              </span>
            </div>
            <div class="rule-actions">
              <button class="btn-icon-small">
                <i class="fas fa-edit"></i>
              </button>
              <button class="btn-icon-small">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .management-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 24px;
    }

    @media (max-width: 393px) {
      .management-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }

    .management-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 20px 16px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
      backdrop-filter: blur(20px);
      border-radius: 16px;
      text-decoration: none;
      color: var(--text-primary);
      box-shadow: var(--card-shadow);
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .management-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    }

    .management-item:hover {
      transform: translateY(-4px);
      box-shadow: var(--card-shadow-hover);
    }

    @media (max-width: 393px) {
      .management-item {
        padding: 16px 12px;
        gap: 10px;
      }
    }

    .management-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }

    .management-info {
      flex: 1;
    }

    .management-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .management-desc {
      font-size: 11px;
      color: var(--text-secondary);
    }

    .management-badge {
      display: flex;
      align-items: center;
    }

    .alert-filters {
      display: flex;
      gap: 8px;
    }

    .filter-btn {
      padding: 4px 12px;
      border: 1px solid var(--border-light);
      background: none;
      border-radius: 6px;
      color: var(--text-secondary);
      font-size: 12px;
      transition: all 0.2s;
    }

    .filter-btn.active {
      background: var(--primary-blue);
      color: white;
      border-color: var(--primary-blue);
    }

    .alert-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .alert-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      border-radius: 8px;
      border-left: 4px solid;
    }

    .alert-item.critical {
      background: #fff5f5;
      border-left-color: var(--danger-red);
    }

    .alert-item.warning {
      background: #fffbf0;
      border-left-color: var(--warning-orange);
    }

    .alert-item.info {
      background: #f0f9ff;
      border-left-color: var(--primary-blue);
    }

    .alert-indicator {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      margin-top: 2px;
    }

    .alert-item.critical .alert-indicator { background: var(--danger-red); }
    .alert-item.warning .alert-indicator { background: var(--warning-orange); }
    .alert-item.info .alert-indicator { background: var(--primary-blue); }

    .alert-content {
      flex: 1;
    }

    .alert-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .alert-desc {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 6px;
    }

    .alert-meta {
      display: flex;
      gap: 12px;
      font-size: 11px;
      color: var(--text-secondary);
    }

    .alert-actions {
      display: flex;
      gap: 8px;
    }

    .btn-action {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 6px;
      background: var(--border-light);
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      transition: all 0.2s;
    }

    .btn-action:hover {
      background: var(--primary-blue);
      color: white;
    }

    .alert-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 12px;
    }

    @media (max-width: 393px) {
      .alert-stats {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
      }
    }

    .stat-card {
      text-align: center;
      padding: 16px 8px;
      border-radius: 12px;
      border: 2px solid;
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    @media (max-width: 393px) {
      .stat-card {
        padding: 12px 6px;
      }
    }

    .stat-card.critical {
      background: #fff5f5;
      border-color: var(--danger-red);
    }

    .stat-card.warning {
      background: #fffbf0;
      border-color: var(--warning-orange);
    }

    .stat-card.info {
      background: #f0f9ff;
      border-color: var(--primary-blue);
    }

    .stat-card.resolved {
      background: #f0fdf4;
      border-color: var(--success-green);
    }

    .stat-number {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 11px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .stat-change {
      font-size: 10px;
      font-weight: 600;
      color: var(--success-green);
    }

    .rule-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .rule-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .rule-info {
      flex: 1;
    }

    .rule-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .rule-condition {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }

    .status-indicator.active {
      color: var(--success-green);
    }

    .status-indicator.inactive {
      color: var(--text-secondary);
    }

    .rule-actions {
      display: flex;
      gap: 8px;
    }

    .btn-icon-small {
      width: 28px;
      height: 28px;
      border: none;
      border-radius: 6px;
      background: var(--border-light);
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      transition: all 0.2s;
    }

    .btn-icon-small:hover {
      background: var(--primary-blue);
      color: white;
    }
  </style>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>实时监控 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="app-content">
    <!-- 页面头部 -->
    <div class="page-header modern-header">
      <div class="header-content">
        <h1 class="page-title">实时监控 📊</h1>
        <p class="page-subtitle">设备状态实时更新</p>
      </div>
      <div class="page-actions">
        <button class="btn-icon modern-btn">
          <i class="fas fa-sync-alt"></i>
        </button>
        <button class="btn-icon modern-btn">
          <i class="fas fa-filter"></i>
        </button>
      </div>
    </div>

    <!-- 能源类型切换 -->
    <div class="energy-tabs mb-16">
      <button class="energy-tab active" data-type="electric">
        <i class="fas fa-bolt"></i>
        <span>电力</span>
      </button>
      <button class="energy-tab" data-type="water">
        <i class="fas fa-tint"></i>
        <span>水务</span>
      </button>
      <button class="energy-tab" data-type="gas">
        <i class="fas fa-fire"></i>
        <span>燃气</span>
      </button>
      <button class="energy-tab" data-type="heat">
        <i class="fas fa-thermometer-half"></i>
        <span>供热</span>
      </button>
    </div>

    <!-- 实时数据仪表 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">实时用电数据</h3>
        <div class="update-time">
          <i class="fas fa-clock"></i>
          <span>更新于 14:25:30</span>
        </div>
      </div>
      <div class="realtime-meters">
        <div class="meter-item">
          <div class="meter-gauge">
            <div class="gauge-circle">
              <div class="gauge-value">1,245</div>
              <div class="gauge-unit">kW</div>
            </div>
          </div>
          <div class="meter-label">当前功率</div>
        </div>
        <div class="meter-item">
          <div class="meter-gauge">
            <div class="gauge-circle">
              <div class="gauge-value">380</div>
              <div class="gauge-unit">V</div>
            </div>
          </div>
          <div class="meter-label">电压</div>
        </div>
        <div class="meter-item">
          <div class="meter-gauge">
            <div class="gauge-circle">
              <div class="gauge-value">0.95</div>
              <div class="gauge-unit">PF</div>
            </div>
          </div>
          <div class="meter-label">功率因数</div>
        </div>
      </div>
    </div>

    <!-- 区域监控 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">区域监控</h3>
        <select class="select-small">
          <option>全部区域</option>
          <option>A栋办公楼</option>
          <option>B栋生产车间</option>
          <option>C栋仓储区</option>
        </select>
      </div>
      <div class="area-monitor-list">
        <div class="area-item">
          <div class="area-info">
            <div class="area-name">A栋办公楼</div>
            <div class="area-status">
              <span class="status-dot status-online"></span>
              <span>正常运行</span>
            </div>
          </div>
          <div class="area-data">
            <div class="data-item">
              <span class="data-label">用电</span>
              <span class="data-value">245 kW</span>
            </div>
            <div class="data-item">
              <span class="data-label">用水</span>
              <span class="data-value">12 m³/h</span>
            </div>
          </div>
          <button class="btn-detail">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <div class="area-item">
          <div class="area-info">
            <div class="area-name">B栋生产车间</div>
            <div class="area-status">
              <span class="status-dot status-warning"></span>
              <span>用电异常</span>
            </div>
          </div>
          <div class="area-data">
            <div class="data-item">
              <span class="data-label">用电</span>
              <span class="data-value warning">856 kW</span>
            </div>
            <div class="data-item">
              <span class="data-label">用水</span>
              <span class="data-value">28 m³/h</span>
            </div>
          </div>
          <button class="btn-detail">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <div class="area-item">
          <div class="area-info">
            <div class="area-name">C栋仓储区</div>
            <div class="area-status">
              <span class="status-dot status-online"></span>
              <span>正常运行</span>
            </div>
          </div>
          <div class="area-data">
            <div class="data-item">
              <span class="data-label">用电</span>
              <span class="data-value">89 kW</span>
            </div>
            <div class="data-item">
              <span class="data-label">用水</span>
              <span class="data-value">3 m³/h</span>
            </div>
          </div>
          <button class="btn-detail">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 设备状态监控 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">设备状态</h3>
        <a href="#" class="link-text">设备管理</a>
      </div>
      <div class="device-monitor-grid">
        <div class="device-monitor-item">
          <div class="device-icon online">
            <i class="fas fa-plug"></i>
          </div>
          <div class="device-info">
            <div class="device-name">电表-001</div>
            <div class="device-location">A栋1楼</div>
            <div class="device-value">245.6 kWh</div>
          </div>
          <div class="device-status">
            <span class="status-dot status-online"></span>
          </div>
        </div>

        <div class="device-monitor-item">
          <div class="device-icon warning">
            <i class="fas fa-tint"></i>
          </div>
          <div class="device-info">
            <div class="device-name">水表-002</div>
            <div class="device-location">B栋2楼</div>
            <div class="device-value">12.3 m³</div>
          </div>
          <div class="device-status">
            <span class="status-dot status-warning"></span>
          </div>
        </div>

        <div class="device-monitor-item">
          <div class="device-icon online">
            <i class="fas fa-fire"></i>
          </div>
          <div class="device-info">
            <div class="device-name">气表-003</div>
            <div class="device-location">C栋地下室</div>
            <div class="device-value">45.8 m³</div>
          </div>
          <div class="device-status">
            <span class="status-dot status-online"></span>
          </div>
        </div>

        <div class="device-monitor-item">
          <div class="device-icon offline">
            <i class="fas fa-thermometer-half"></i>
          </div>
          <div class="device-info">
            <div class="device-name">温度传感器-004</div>
            <div class="device-location">A栋3楼</div>
            <div class="device-value">-- °C</div>
          </div>
          <div class="device-status">
            <span class="status-dot status-offline"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时告警 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">实时告警</h3>
        <div class="alert-count">
          <span class="badge badge-danger">3</span>
        </div>
      </div>
      <div class="realtime-alerts">
        <div class="alert-item urgent">
          <div class="alert-indicator">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">B栋用电功率超限</div>
            <div class="alert-desc">当前功率856kW，超过阈值800kW</div>
            <div class="alert-time">刚刚</div>
          </div>
          <button class="alert-action">
            <i class="fas fa-eye"></i>
          </button>
        </div>

        <div class="alert-item warning">
          <div class="alert-indicator">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">水表通信异常</div>
            <div class="alert-desc">水表-002连接中断</div>
            <div class="alert-time">2分钟前</div>
          </div>
          <button class="alert-action">
            <i class="fas fa-eye"></i>
          </button>
        </div>

        <div class="alert-item info">
          <div class="alert-indicator">
            <i class="fas fa-info-circle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">设备维护提醒</div>
            <div class="alert-desc">电表-001需要定期维护</div>
            <div class="alert-time">10分钟前</div>
          </div>
          <button class="alert-action">
            <i class="fas fa-eye"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <style>
    /* 现代化页面头部 */
    .modern-header {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 20px;
      margin-bottom: 24px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header-content {
      margin-bottom: 12px;
    }

    .page-title {
      font-size: 24px;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 4px;
    }

    .page-subtitle {
      font-size: 14px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .modern-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 12px;
      padding: 12px;
      color: var(--text-primary);
      transition: all 0.3s ease;
      margin-left: 8px;
    }

    .modern-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
    }

    .energy-tabs {
      display: flex;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
      backdrop-filter: blur(20px);
      border-radius: 16px;
      padding: 6px;
      gap: 4px;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .energy-tab {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 12px 8px;
      border: none;
      background: none;
      border-radius: 8px;
      color: var(--text-secondary);
      font-size: 12px;
      transition: all 0.2s;
    }

    .energy-tab.active {
      background: var(--primary-blue);
      color: white;
    }

    .energy-tab i {
      font-size: 16px;
    }

    .update-time {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: var(--text-secondary);
    }

    .realtime-meters {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 16px;
    }

    .meter-item {
      text-align: center;
    }

    .meter-gauge {
      margin-bottom: 8px;
    }

    .gauge-circle {
      width: 80px;
      height: 80px;
      border: 4px solid var(--primary-blue);
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .gauge-value {
      font-size: 16px;
      font-weight: 700;
      color: var(--text-primary);
    }

    .gauge-unit {
      font-size: 10px;
      color: var(--text-secondary);
    }

    .meter-label {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .area-monitor-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .area-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .area-info {
      flex: 1;
    }

    .area-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .area-status {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: var(--text-secondary);
    }

    .area-data {
      display: flex;
      flex-direction: column;
      gap: 4px;
      min-width: 80px;
    }

    .data-item {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
    }

    .data-label {
      color: var(--text-secondary);
    }

    .data-value {
      font-weight: 600;
    }

    .data-value.warning {
      color: var(--warning-orange);
    }

    .btn-detail {
      background: none;
      border: none;
      color: var(--text-secondary);
      font-size: 12px;
      padding: 4px;
    }

    .device-monitor-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .device-monitor-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .device-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }

    .device-icon.online { background: var(--success-green); }
    .device-icon.warning { background: var(--warning-orange); }
    .device-icon.offline { background: var(--text-secondary); }

    .device-info {
      flex: 1;
    }

    .device-name {
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .device-location {
      font-size: 10px;
      color: var(--text-secondary);
      margin-bottom: 2px;
    }

    .device-value {
      font-size: 11px;
      font-weight: 600;
      color: var(--primary-blue);
    }

    .device-status {
      display: flex;
      align-items: center;
    }

    .alert-count {
      display: flex;
      align-items: center;
    }

    .realtime-alerts {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .alert-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      border-radius: 8px;
      border-left: 4px solid;
    }

    .alert-item.urgent {
      background: #fff5f5;
      border-left-color: var(--danger-red);
    }

    .alert-item.warning {
      background: #fffbf0;
      border-left-color: var(--warning-orange);
    }

    .alert-item.info {
      background: #f0f9ff;
      border-left-color: var(--primary-blue);
    }

    .alert-indicator {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      margin-top: 2px;
    }

    .alert-item.urgent .alert-indicator { background: var(--danger-red); }
    .alert-item.warning .alert-indicator { background: var(--warning-orange); }
    .alert-item.info .alert-indicator { background: var(--primary-blue); }

    .alert-content {
      flex: 1;
    }

    .alert-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .alert-desc {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .alert-time {
      font-size: 11px;
      color: var(--text-secondary);
    }

    .alert-action {
      background: none;
      border: none;
      color: var(--text-secondary);
      font-size: 14px;
      padding: 4px;
    }
  </style>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>个人中心 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="app-content">
    <!-- 用户信息卡片 -->
    <div class="user-profile-card mb-16">
      <div class="user-avatar">
        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="用户头像">
        <button class="avatar-edit-btn">
          <i class="fas fa-camera"></i>
        </button>
      </div>
      <div class="user-info">
        <h2 class="user-name">张三</h2>
        <p class="user-title">能源管理员</p>
        <p class="user-department">运营管理部</p>
      </div>
      <div class="user-stats">
        <div class="stat-item">
          <div class="stat-value">156</div>
          <div class="stat-label">处理告警</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">23</div>
          <div class="stat-label">生成报表</div>
        </div>
      </div>
    </div>

    <!-- 快捷功能 -->
    <div class="quick-functions mb-16">
      <div class="function-item">
        <div class="function-icon" style="background: var(--primary-blue);">
          <i class="fas fa-chart-line"></i>
        </div>
        <span class="function-label">我的报表</span>
      </div>
      <div class="function-item">
        <div class="function-icon" style="background: var(--success-green);">
          <i class="fas fa-tasks"></i>
        </div>
        <span class="function-label">我的任务</span>
      </div>
      <div class="function-item">
        <div class="function-icon" style="background: var(--warning-orange);">
          <i class="fas fa-bell"></i>
        </div>
        <span class="function-label">我的告警</span>
      </div>
      <div class="function-item">
        <div class="function-icon" style="background: var(--danger-red);">
          <i class="fas fa-history"></i>
        </div>
        <span class="function-label">操作记录</span>
      </div>
    </div>

    <!-- 系统设置 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">系统设置</h3>
      </div>
      <div class="settings-list">
        <div class="setting-item">
          <div class="setting-icon">
            <i class="fas fa-bell"></i>
          </div>
          <div class="setting-info">
            <div class="setting-title">通知设置</div>
            <div class="setting-desc">管理推送通知和提醒</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input type="checkbox" checked>
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-icon">
            <i class="fas fa-moon"></i>
          </div>
          <div class="setting-info">
            <div class="setting-title">深色模式</div>
            <div class="setting-desc">切换应用主题外观</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input type="checkbox">
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-icon">
            <i class="fas fa-language"></i>
          </div>
          <div class="setting-info">
            <div class="setting-title">语言设置</div>
            <div class="setting-desc">选择应用显示语言</div>
          </div>
          <div class="setting-control">
            <select class="setting-select">
              <option>简体中文</option>
              <option>English</option>
              <option>繁體中文</option>
            </select>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-icon">
            <i class="fas fa-download"></i>
          </div>
          <div class="setting-info">
            <div class="setting-title">自动下载</div>
            <div class="setting-desc">自动下载报表和数据</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input type="checkbox" checked>
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 账户管理 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">账户管理</h3>
      </div>
      <div class="account-list">
        <a href="#" class="account-item">
          <div class="account-icon">
            <i class="fas fa-user-edit"></i>
          </div>
          <div class="account-info">
            <div class="account-title">编辑资料</div>
            <div class="account-desc">修改个人信息和头像</div>
          </div>
          <div class="account-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>

        <a href="#" class="account-item">
          <div class="account-icon">
            <i class="fas fa-lock"></i>
          </div>
          <div class="account-info">
            <div class="account-title">修改密码</div>
            <div class="account-desc">更改登录密码</div>
          </div>
          <div class="account-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>

        <a href="#" class="account-item">
          <div class="account-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <div class="account-info">
            <div class="account-title">安全设置</div>
            <div class="account-desc">两步验证、登录记录</div>
          </div>
          <div class="account-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>

        <a href="#" class="account-item">
          <div class="account-icon">
            <i class="fas fa-users-cog"></i>
          </div>
          <div class="account-info">
            <div class="account-title">权限管理</div>
            <div class="account-desc">查看和申请权限</div>
          </div>
          <div class="account-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>
      </div>
    </div>

    <!-- 应用信息 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">应用信息</h3>
      </div>
      <div class="app-info-list">
        <div class="info-item">
          <span class="info-label">应用版本</span>
          <span class="info-value">v2.1.0</span>
        </div>
        <div class="info-item">
          <span class="info-label">最后更新</span>
          <span class="info-value">2024-01-15</span>
        </div>
        <div class="info-item">
          <span class="info-label">数据同步</span>
          <span class="info-value">
            <span class="sync-status online">已同步</span>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">存储空间</span>
          <span class="info-value">156MB / 500MB</span>
        </div>
      </div>
    </div>

    <!-- 帮助与支持 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">帮助与支持</h3>
      </div>
      <div class="help-list">
        <a href="#" class="help-item">
          <div class="help-icon">
            <i class="fas fa-question-circle"></i>
          </div>
          <span class="help-title">使用帮助</span>
          <div class="help-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>

        <a href="#" class="help-item">
          <div class="help-icon">
            <i class="fas fa-comments"></i>
          </div>
          <span class="help-title">意见反馈</span>
          <div class="help-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>

        <a href="#" class="help-item">
          <div class="help-icon">
            <i class="fas fa-phone"></i>
          </div>
          <span class="help-title">联系客服</span>
          <div class="help-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>

        <a href="#" class="help-item">
          <div class="help-icon">
            <i class="fas fa-file-contract"></i>
          </div>
          <span class="help-title">隐私政策</span>
          <div class="help-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </a>
      </div>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <button class="logout-btn">
        <i class="fas fa-sign-out-alt"></i>
        <span>退出登录</span>
      </button>
    </div>
  </div>

  <style>
    .user-profile-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 32px 20px;
      color: white;
      position: relative;
      overflow: hidden;
      box-shadow: var(--card-shadow-hover);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .user-profile-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    }

    .user-profile-card::after {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200px;
      height: 200px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }

    .user-avatar {
      position: relative;
      width: 80px;
      height: 80px;
      margin: 0 auto 16px;
    }

    .user-avatar img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .avatar-edit-btn {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 28px;
      height: 28px;
      background: var(--warning-orange);
      border: none;
      border-radius: 50%;
      color: white;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-info {
      text-align: center;
      margin-bottom: 20px;
    }

    .user-name {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .user-title {
      font-size: 14px;
      opacity: 0.9;
      margin-bottom: 2px;
    }

    .user-department {
      font-size: 12px;
      opacity: 0.7;
    }

    .user-stats {
      display: flex;
      justify-content: center;
      gap: 32px;
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: 18px;
      font-weight: 700;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 11px;
      opacity: 0.8;
    }

    .quick-functions {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 16px;
    }

    .function-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 16px 8px;
      background: var(--card-white);
      border-radius: 12px;
      text-decoration: none;
      color: var(--text-primary);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .function-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }

    .function-label {
      font-size: 12px;
      text-align: center;
    }

    .settings-list {
      display: flex;
      flex-direction: column;
    }

    .setting-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 0;
      border-bottom: 1px solid var(--border-light);
    }

    .setting-item:last-child {
      border-bottom: none;
    }

    .setting-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--background-gray);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      font-size: 16px;
    }

    .setting-info {
      flex: 1;
    }

    .setting-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .setting-desc {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .setting-control {
      display: flex;
      align-items: center;
    }

    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--border-light);
      transition: .4s;
      border-radius: 24px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .slider {
      background-color: var(--primary-blue);
    }

    input:checked + .slider:before {
      transform: translateX(20px);
    }

    .setting-select {
      padding: 6px 12px;
      border: 1px solid var(--border-light);
      border-radius: 6px;
      background: var(--card-white);
      font-size: 12px;
      color: var(--text-primary);
    }

    .account-list, .help-list {
      display: flex;
      flex-direction: column;
    }

    .account-item, .help-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 0;
      border-bottom: 1px solid var(--border-light);
      text-decoration: none;
      color: var(--text-primary);
    }

    .account-item:last-child, .help-item:last-child {
      border-bottom: none;
    }

    .account-icon, .help-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--background-gray);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
      font-size: 16px;
    }

    .account-info {
      flex: 1;
    }

    .account-title, .help-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .account-desc {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .account-arrow, .help-arrow {
      color: var(--text-secondary);
      font-size: 12px;
    }

    .app-info-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
    }

    .info-label {
      font-size: 14px;
      color: var(--text-secondary);
    }

    .info-value {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .sync-status {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }

    .sync-status.online {
      color: var(--success-green);
    }

    .sync-status.online::before {
      content: '';
      width: 6px;
      height: 6px;
      background: var(--success-green);
      border-radius: 50%;
    }

    .logout-section {
      text-align: center;
      padding: 20px 0;
    }

    .logout-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      padding: 16px;
      background: var(--danger-red);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.2s;
    }

    .logout-btn:hover {
      background: #e53e3e;
    }
  </style>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>首页 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="app-content">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="greeting-section">
          <h1 class="page-title">早上好 👋</h1>
          <p class="page-subtitle">今日 2024年7月15日</p>
        </div>
        <div class="weather-info">
          <div class="weather-icon">☀️</div>
          <div class="weather-temp">28°C</div>
        </div>
      </div>
      <div class="page-actions">
        <button class="btn-icon notification-btn">
          <i class="fas fa-bell"></i>
          <span class="badge badge-danger">3</span>
        </button>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card electric">
        <div class="metric-header">
          <div class="metric-icon">
            <i class="fas fa-bolt"></i>
          </div>
          <div class="metric-trend">
            <span class="trend-icon positive">↗</span>
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-value">1,245.6</div>
          <div class="metric-unit">kWh</div>
        </div>
        <div class="metric-footer">
          <div class="metric-label">今日用电</div>
          <div class="metric-change positive">+5.2%</div>
        </div>
      </div>

      <div class="metric-card water">
        <div class="metric-header">
          <div class="metric-icon">
            <i class="fas fa-tint"></i>
          </div>
          <div class="metric-trend">
            <span class="trend-icon negative">↘</span>
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-value">856.3</div>
          <div class="metric-unit">m³</div>
        </div>
        <div class="metric-footer">
          <div class="metric-label">今日用水</div>
          <div class="metric-change negative">-2.1%</div>
        </div>
      </div>

      <div class="metric-card gas">
        <div class="metric-header">
          <div class="metric-icon">
            <i class="fas fa-fire"></i>
          </div>
          <div class="metric-trend">
            <span class="trend-icon positive">↗</span>
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-value">432.8</div>
          <div class="metric-unit">m³</div>
        </div>
        <div class="metric-footer">
          <div class="metric-label">今日用气</div>
          <div class="metric-change positive">****%</div>
        </div>
      </div>

      <div class="metric-card carbon">
        <div class="metric-header">
          <div class="metric-icon">
            <i class="fas fa-leaf"></i>
          </div>
          <div class="metric-trend">
            <span class="trend-icon warning">↗</span>
          </div>
        </div>
        <div class="metric-content">
          <div class="metric-value">2.34</div>
          <div class="metric-unit">t CO₂</div>
        </div>
        <div class="metric-footer">
          <div class="metric-label">碳排放</div>
          <div class="metric-change warning">+3.5%</div>
        </div>
      </div>
    </div>

    <!-- 能耗趋势图表 -->
    <div class="card chart-card">
      <div class="card-header">
        <h3 class="card-title">7日能耗趋势</h3>
        <select class="select-small" id="energyTypeSelect">
          <option value="electric">用电量</option>
          <option value="water">用水量</option>
          <option value="gas">用气量</option>
        </select>
      </div>
      <div class="chart-container">
        <canvas id="trendChart" width="320" height="180"></canvas>
      </div>
      <div class="chart-summary">
        <div class="summary-item">
          <span class="summary-label">本周总计</span>
          <span class="summary-value" id="weekTotal">8,456 kWh</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">日均消耗</span>
          <span class="summary-value" id="dailyAvg">1,208 kWh</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">较上周</span>
          <span class="summary-value trend-up" id="weekChange">+5.2%</span>
        </div>
      </div>
    </div>

    <!-- 设备状态概览 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">设备状态</h3>
        <a href="#" class="link-text">查看全部</a>
      </div>
      <div class="device-status-grid">
        <div class="device-status-item">
          <div class="status-indicator">
            <span class="status-dot status-online"></span>
          </div>
          <div class="device-info">
            <div class="device-name">电表设备</div>
            <div class="device-count">45/48 在线</div>
          </div>
        </div>
        <div class="device-status-item">
          <div class="status-indicator">
            <span class="status-dot status-warning"></span>
          </div>
          <div class="device-info">
            <div class="device-name">水表设备</div>
            <div class="device-count">23/25 在线</div>
          </div>
        </div>
        <div class="device-status-item">
          <div class="status-indicator">
            <span class="status-dot status-online"></span>
          </div>
          <div class="device-info">
            <div class="device-name">气表设备</div>
            <div class="device-count">12/12 在线</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警信息 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">最新告警</h3>
        <a href="#" class="link-text">查看全部</a>
      </div>
      <div class="alert-list">
        <div class="alert-item">
          <div class="alert-icon alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">A栋3楼用电异常</div>
            <div class="alert-time">2分钟前</div>
          </div>
          <div class="alert-status">
            <span class="status-badge status-danger">严重</span>
          </div>
        </div>
        <div class="alert-item">
          <div class="alert-icon alert-warning">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">B栋水压偏低</div>
            <div class="alert-time">15分钟前</div>
          </div>
          <div class="alert-status">
            <span class="status-badge status-warning">警告</span>
          </div>
        </div>
        <div class="alert-item">
          <div class="alert-icon alert-info">
            <i class="fas fa-info-circle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">设备维护提醒</div>
            <div class="alert-time">1小时前</div>
          </div>
          <div class="alert-status">
            <span class="status-badge status-info">提醒</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">快捷操作</h3>
      </div>
      <div class="quick-actions">
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--primary-blue);">
            <i class="fas fa-chart-line"></i>
          </div>
          <span class="quick-action-label">实时监控</span>
        </a>
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--success-green);">
            <i class="fas fa-file-alt"></i>
          </div>
          <span class="quick-action-label">生成报表</span>
        </a>
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--warning-orange);">
            <i class="fas fa-cog"></i>
          </div>
          <span class="quick-action-label">设备管理</span>
        </a>
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--danger-red);">
            <i class="fas fa-leaf"></i>
          </div>
          <span class="quick-action-label">碳排管理</span>
        </a>
      </div>
    </div>
  </div>

  <style>
    /* 页面头部样式 */
    .page-header {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 20px;
      margin-bottom: 24px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .greeting-section .page-title {
      font-size: 24px;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 4px;
    }

    .page-subtitle {
      font-size: 14px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .weather-info {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.2);
      padding: 8px 12px;
      border-radius: 12px;
    }

    .weather-icon {
      font-size: 18px;
    }

    .weather-temp {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .notification-btn {
      position: relative;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 12px;
      padding: 12px;
      color: var(--text-primary);
      transition: all 0.3s ease;
    }

    .notification-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
    }

    .notification-btn .badge {
      position: absolute;
      top: -2px;
      right: -2px;
      transform: none;
    }

    /* 指标卡片网格 */
    .metrics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 24px;
    }

    .metric-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
      backdrop-filter: blur(20px);
      border-radius: 20px;
      padding: 20px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .metric-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
      pointer-events: none;
    }

    .metric-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .metric-card.electric {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }

    .metric-card.water {
      background: linear-gradient(135deg, #11998e, #38ef7d);
      color: white;
    }

    .metric-card.gas {
      background: linear-gradient(135deg, #f093fb, #f5576c);
      color: white;
    }

    .metric-card.carbon {
      background: linear-gradient(135deg, #fc466b, #3f5efb);
      color: white;
    }

    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .metric-icon {
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
    }

    .metric-trend {
      font-size: 16px;
      font-weight: 600;
    }

    .trend-icon.positive {
      color: #4ade80;
    }

    .trend-icon.negative {
      color: #f87171;
    }

    .trend-icon.warning {
      color: #fbbf24;
    }

    .metric-content {
      display: flex;
      align-items: baseline;
      gap: 4px;
      margin-bottom: 12px;
    }

    .metric-value {
      font-size: 28px;
      font-weight: 700;
      line-height: 1;
    }

    .metric-unit {
      font-size: 14px;
      opacity: 0.8;
      font-weight: 500;
    }

    .metric-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .metric-label {
      font-size: 13px;
      opacity: 0.9;
      font-weight: 500;
    }

    .metric-change {
      font-size: 12px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.2);
    }

    .metric-change.positive {
      color: #4ade80;
    }

    .metric-change.negative {
      color: #f87171;
    }

    .metric-change.warning {
      color: #fbbf24;
    }

    .select-small {
      padding: 8px 12px;
      border: 1px solid var(--border-light);
      border-radius: 8px;
      background: var(--card-white);
      font-size: 13px;
      color: var(--text-primary);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .select-small:focus {
      outline: none;
      border-color: var(--primary-blue);
      box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
    }

    .chart-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
      backdrop-filter: blur(20px);
    }

    .chart-container {
      height: 180px;
      margin: 16px 0;
      position: relative;
    }

    .chart-summary {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 16px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--border-light);
    }

    .summary-item {
      text-align: center;
    }

    .summary-label {
      display: block;
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
      font-weight: 500;
    }

    .summary-value {
      font-size: 16px;
      font-weight: 700;
      color: var(--text-primary);
    }

    .summary-value.trend-up {
      color: var(--success-green);
    }

    .summary-value.trend-down {
      color: var(--danger-red);
    }

    .device-status-grid {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .device-status-item {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .device-info {
      flex: 1;
    }

    .device-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .device-count {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .alert-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .alert-item {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .alert-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .alert-danger { background: var(--danger-red); }
    .alert-warning { background: var(--warning-orange); }
    .alert-info { background: var(--primary-blue); }

    .alert-content {
      flex: 1;
    }

    .alert-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .alert-time {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .status-badge {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
      color: white;
    }

    .status-danger { background: var(--danger-red); }
    .status-warning { background: var(--warning-orange); }
    .status-info { background: var(--primary-blue); }

    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 16px;
    }

    .quick-action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      text-decoration: none;
      color: var(--text-primary);
    }

    .quick-action-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }

    .quick-action-label {
      font-size: 12px;
      text-align: center;
    }

    .link-text {
      color: var(--primary-blue);
      text-decoration: none;
      font-size: 14px;
    }
  </style>

  <script>
    // 图表数据
    const chartData = {
      electric: {
        labels: ['7/9', '7/10', '7/11', '7/12', '7/13', '7/14', '7/15'],
        data: [1180, 1220, 1156, 1289, 1345, 1198, 1245],
        color: '#667eea',
        unit: 'kWh',
        total: '8,633 kWh',
        avg: '1,233 kWh',
        change: '+5.2%'
      },
      water: {
        labels: ['7/9', '7/10', '7/11', '7/12', '7/13', '7/14', '7/15'],
        data: [820, 856, 798, 912, 889, 834, 856],
        color: '#11998e',
        unit: 'm³',
        total: '5,965 m³',
        avg: '852 m³',
        change: '-2.1%'
      },
      gas: {
        labels: ['7/9', '7/10', '7/11', '7/12', '7/13', '7/14', '7/15'],
        data: [420, 445, 398, 467, 456, 425, 432],
        color: '#f093fb',
        unit: 'm³',
        total: '3,043 m³',
        avg: '435 m³',
        change: '****%'
      }
    };

    let chart;

    // 初始化图表
    function initChart() {
      const ctx = document.getElementById('trendChart').getContext('2d');

      chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: chartData.electric.labels,
          datasets: [{
            label: '用电量',
            data: chartData.electric.data,
            borderColor: chartData.electric.color,
            backgroundColor: chartData.electric.color + '20',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: chartData.electric.color,
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#6B7280',
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            },
            y: {
              grid: {
                color: '#E5E7EB',
                lineWidth: 1
              },
              ticks: {
                color: '#6B7280',
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            }
          },
          elements: {
            point: {
              hoverBackgroundColor: '#ffffff'
            }
          }
        }
      });
    }

    // 更新图表
    function updateChart(type) {
      const data = chartData[type];
      chart.data.labels = data.labels;
      chart.data.datasets[0].data = data.data;
      chart.data.datasets[0].borderColor = data.color;
      chart.data.datasets[0].backgroundColor = data.color + '20';
      chart.data.datasets[0].pointBackgroundColor = data.color;
      chart.update();

      // 更新摘要数据
      document.getElementById('weekTotal').textContent = data.total;
      document.getElementById('dailyAvg').textContent = data.avg;
      document.getElementById('weekChange').textContent = data.change;

      // 更新趋势颜色
      const changeElement = document.getElementById('weekChange');
      changeElement.className = 'summary-value ' + (data.change.startsWith('+') ? 'trend-up' : 'trend-down');
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      initChart();

      // 绑定选择器事件
      document.getElementById('energyTypeSelect').addEventListener('change', function(e) {
        updateChart(e.target.value);
      });
    });
  </script>
</body>
</html>

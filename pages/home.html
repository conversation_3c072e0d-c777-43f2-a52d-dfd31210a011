<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>首页 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="app-content">
    <!-- 页面头部 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">能源总览</h1>
        <p class="page-subtitle">今日 2024-01-15</p>
      </div>
      <div class="page-actions">
        <button class="btn-icon">
          <i class="fas fa-bell"></i>
          <span class="badge badge-danger">3</span>
        </button>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="grid grid-2 mb-16">
      <div class="metric-card">
        <div class="metric-icon" style="background: var(--primary-blue);">
          <i class="fas fa-bolt"></i>
        </div>
        <div class="metric-value" style="color: var(--primary-blue);">1,245</div>
        <div class="metric-label">今日用电 (kWh)</div>
        <div class="metric-change positive">↑ 5.2%</div>
      </div>
      <div class="metric-card">
        <div class="metric-icon" style="background: var(--success-green);">
          <i class="fas fa-tint"></i>
        </div>
        <div class="metric-value" style="color: var(--success-green);">856</div>
        <div class="metric-label">今日用水 (m³)</div>
        <div class="metric-change negative">↓ 2.1%</div>
      </div>
    </div>

    <div class="grid grid-2 mb-16">
      <div class="metric-card">
        <div class="metric-icon" style="background: var(--warning-orange);">
          <i class="fas fa-fire"></i>
        </div>
        <div class="metric-value" style="color: var(--warning-orange);">432</div>
        <div class="metric-label">今日用气 (m³)</div>
        <div class="metric-change positive">↑ 1.8%</div>
      </div>
      <div class="metric-card">
        <div class="metric-icon" style="background: var(--danger-red);">
          <i class="fas fa-leaf"></i>
        </div>
        <div class="metric-value" style="color: var(--danger-red);">2.3</div>
        <div class="metric-label">碳排放 (t)</div>
        <div class="metric-change positive">↑ 3.5%</div>
      </div>
    </div>

    <!-- 能耗趋势图表 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">7日能耗趋势</h3>
        <select class="select-small">
          <option>用电量</option>
          <option>用水量</option>
          <option>用气量</option>
        </select>
      </div>
      <div class="chart-container">
        <canvas id="trendChart" width="300" height="150"></canvas>
      </div>
    </div>

    <!-- 设备状态概览 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">设备状态</h3>
        <a href="#" class="link-text">查看全部</a>
      </div>
      <div class="device-status-grid">
        <div class="device-status-item">
          <div class="status-indicator">
            <span class="status-dot status-online"></span>
          </div>
          <div class="device-info">
            <div class="device-name">电表设备</div>
            <div class="device-count">45/48 在线</div>
          </div>
        </div>
        <div class="device-status-item">
          <div class="status-indicator">
            <span class="status-dot status-warning"></span>
          </div>
          <div class="device-info">
            <div class="device-name">水表设备</div>
            <div class="device-count">23/25 在线</div>
          </div>
        </div>
        <div class="device-status-item">
          <div class="status-indicator">
            <span class="status-dot status-online"></span>
          </div>
          <div class="device-info">
            <div class="device-name">气表设备</div>
            <div class="device-count">12/12 在线</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警信息 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">最新告警</h3>
        <a href="#" class="link-text">查看全部</a>
      </div>
      <div class="alert-list">
        <div class="alert-item">
          <div class="alert-icon alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">A栋3楼用电异常</div>
            <div class="alert-time">2分钟前</div>
          </div>
          <div class="alert-status">
            <span class="status-badge status-danger">严重</span>
          </div>
        </div>
        <div class="alert-item">
          <div class="alert-icon alert-warning">
            <i class="fas fa-exclamation-circle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">B栋水压偏低</div>
            <div class="alert-time">15分钟前</div>
          </div>
          <div class="alert-status">
            <span class="status-badge status-warning">警告</span>
          </div>
        </div>
        <div class="alert-item">
          <div class="alert-icon alert-info">
            <i class="fas fa-info-circle"></i>
          </div>
          <div class="alert-content">
            <div class="alert-title">设备维护提醒</div>
            <div class="alert-time">1小时前</div>
          </div>
          <div class="alert-status">
            <span class="status-badge status-info">提醒</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">快捷操作</h3>
      </div>
      <div class="quick-actions">
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--primary-blue);">
            <i class="fas fa-chart-line"></i>
          </div>
          <span class="quick-action-label">实时监控</span>
        </a>
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--success-green);">
            <i class="fas fa-file-alt"></i>
          </div>
          <span class="quick-action-label">生成报表</span>
        </a>
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--warning-orange);">
            <i class="fas fa-cog"></i>
          </div>
          <span class="quick-action-label">设备管理</span>
        </a>
        <a href="#" class="quick-action-item">
          <div class="quick-action-icon" style="background: var(--danger-red);">
            <i class="fas fa-leaf"></i>
          </div>
          <span class="quick-action-label">碳排管理</span>
        </a>
      </div>
    </div>
  </div>

  <style>
    .page-subtitle {
      font-size: 14px;
      color: var(--text-secondary);
      margin-top: 4px;
    }

    .btn-icon {
      position: relative;
      background: none;
      border: none;
      font-size: 20px;
      color: var(--text-primary);
      padding: 8px;
    }

    .btn-icon .badge {
      position: absolute;
      top: 0;
      right: 0;
      transform: translate(50%, -50%);
    }

    .metric-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      color: white;
      font-size: 16px;
    }

    .select-small {
      padding: 4px 8px;
      border: 1px solid var(--border-light);
      border-radius: 6px;
      background: var(--card-white);
      font-size: 12px;
    }

    .chart-container {
      height: 150px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
    }

    .device-status-grid {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .device-status-item {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .device-info {
      flex: 1;
    }

    .device-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .device-count {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .alert-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .alert-item {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .alert-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .alert-danger { background: var(--danger-red); }
    .alert-warning { background: var(--warning-orange); }
    .alert-info { background: var(--primary-blue); }

    .alert-content {
      flex: 1;
    }

    .alert-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .alert-time {
      font-size: 12px;
      color: var(--text-secondary);
    }

    .status-badge {
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
      color: white;
    }

    .status-danger { background: var(--danger-red); }
    .status-warning { background: var(--warning-orange); }
    .status-info { background: var(--primary-blue); }

    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 16px;
    }

    .quick-action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      text-decoration: none;
      color: var(--text-primary);
    }

    .quick-action-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }

    .quick-action-label {
      font-size: 12px;
      text-align: center;
    }

    .link-text {
      color: var(--primary-blue);
      text-decoration: none;
      font-size: 14px;
    }
  </style>
</body>
</html>

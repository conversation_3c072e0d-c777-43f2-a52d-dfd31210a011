<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据分析 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="app-content">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">数据分析</h1>
      <div class="page-actions">
        <button class="btn-icon">
          <i class="fas fa-download"></i>
        </button>
        <button class="btn-icon">
          <i class="fas fa-share-alt"></i>
        </button>
      </div>
    </div>

    <!-- 时间范围选择 - 全新设计 -->
    <div class="time-selector-premium mb-16">
      <div class="selector-header">
        <div class="header-left">
          <div class="time-icon-wrapper">
            <i class="fas fa-calendar-check"></i>
          </div>
          <div class="header-text">
            <h3 class="selector-title">时间范围</h3>
            <p class="selector-subtitle">选择数据分析周期</p>
          </div>
        </div>
        <button class="custom-date-btn">
          <i class="fas fa-sliders-h"></i>
          <span>自定义</span>
        </button>
      </div>

      <div class="time-options-grid">
        <div class="time-option" data-period="day">
          <div class="option-header">
            <div class="option-icon today">
              <i class="fas fa-sun"></i>
            </div>
            <div class="option-badge">实时</div>
          </div>
          <div class="option-content">
            <h4 class="option-title">今日</h4>
            <p class="option-desc">7月15日</p>
            <div class="option-stats">
              <span class="stats-value">24h</span>
              <span class="stats-label">数据</span>
            </div>
          </div>
        </div>

        <div class="time-option" data-period="week">
          <div class="option-header">
            <div class="option-icon week">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="option-badge">趋势</div>
          </div>
          <div class="option-content">
            <h4 class="option-title">本周</h4>
            <p class="option-desc">7月9日 - 7月15日</p>
            <div class="option-stats">
              <span class="stats-value">7天</span>
              <span class="stats-label">数据</span>
            </div>
          </div>
        </div>

        <div class="time-option active" data-period="month">
          <div class="option-header">
            <div class="option-icon month">
              <i class="fas fa-chart-bar"></i>
            </div>
            <div class="option-badge">分析</div>
          </div>
          <div class="option-content">
            <h4 class="option-title">本月</h4>
            <p class="option-desc">2024年7月</p>
            <div class="option-stats">
              <span class="stats-value">31天</span>
              <span class="stats-label">数据</span>
            </div>
          </div>
          <div class="active-indicator"></div>
        </div>

        <div class="time-option" data-period="year">
          <div class="option-header">
            <div class="option-icon year">
              <i class="fas fa-chart-area"></i>
            </div>
            <div class="option-badge">总览</div>
          </div>
          <div class="option-content">
            <h4 class="option-title">本年</h4>
            <p class="option-desc">2024年全年</p>
            <div class="option-stats">
              <span class="stats-value">365天</span>
              <span class="stats-label">数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 能耗对比分析 -->
    <div class="card comparison-card mb-16">
      <div class="card-header">
        <h3 class="card-title">能耗对比分析</h3>
        <select class="select-small" id="comparisonType">
          <option value="monthly">同比分析</option>
          <option value="weekly">环比分析</option>
          <option value="target">目标对比</option>
        </select>
      </div>
      <div class="comparison-chart">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color current"></span>
            <span id="currentPeriod">本月</span>
          </div>
          <div class="legend-item">
            <span class="legend-color previous"></span>
            <span id="previousPeriod">上月</span>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="comparisonChart" width="320" height="160"></canvas>
        </div>
        <div class="comparison-summary">
          <div class="summary-item electric">
            <div class="summary-icon">
              <i class="fas fa-bolt"></i>
            </div>
            <div class="summary-content">
              <span class="summary-label">用电量变化</span>
              <span class="summary-value positive" id="electricChange">+5.2%</span>
            </div>
          </div>
          <div class="summary-item water">
            <div class="summary-icon">
              <i class="fas fa-tint"></i>
            </div>
            <div class="summary-content">
              <span class="summary-label">用水量变化</span>
              <span class="summary-value negative" id="waterChange">-2.1%</span>
            </div>
          </div>
          <div class="summary-item gas">
            <div class="summary-icon">
              <i class="fas fa-fire"></i>
            </div>
            <div class="summary-content">
              <span class="summary-label">用气量变化</span>
              <span class="summary-value positive" id="gasChange">+1.8%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 能耗排名 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">区域能耗排名</h3>
        <div class="ranking-tabs">
          <button class="ranking-tab active" data-type="electric">用电</button>
          <button class="ranking-tab" data-type="water">用水</button>
          <button class="ranking-tab" data-type="gas">用气</button>
        </div>
      </div>
      <div class="ranking-list">
        <div class="ranking-item">
          <div class="ranking-number first">1</div>
          <div class="ranking-info">
            <div class="ranking-name">B栋生产车间</div>
            <div class="ranking-detail">占总用电量 45.2%</div>
          </div>
          <div class="ranking-value">
            <span class="value">12,456</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-up trend-up"></i>
            <span>8.5%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number second">2</div>
          <div class="ranking-info">
            <div class="ranking-name">A栋办公楼</div>
            <div class="ranking-detail">占总用电量 32.1%</div>
          </div>
          <div class="ranking-value">
            <span class="value">8,845</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-down trend-down"></i>
            <span>2.3%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number third">3</div>
          <div class="ranking-info">
            <div class="ranking-name">C栋仓储区</div>
            <div class="ranking-detail">占总用电量 22.7%</div>
          </div>
          <div class="ranking-value">
            <span class="value">6,234</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-up trend-up"></i>
            <span>1.2%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number">4</div>
          <div class="ranking-info">
            <div class="ranking-name">D栋研发中心</div>
            <div class="ranking-detail">占总用电量 15.6%</div>
          </div>
          <div class="ranking-value">
            <span class="value">4,298</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-minus trend-stable"></i>
            <span>0.1%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 峰谷分析 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">用电峰谷分析</h3>
        <select class="select-small">
          <option>本周</option>
          <option>本月</option>
          <option>本年</option>
        </select>
      </div>
      <div class="peak-valley-analysis">
        <div class="peak-valley-chart">
          <canvas id="peakValleyChart" width="300" height="100"></canvas>
        </div>
        <div class="peak-valley-stats">
          <div class="stat-item">
            <div class="stat-icon peak">
              <i class="fas fa-arrow-up"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">峰时用电</div>
              <div class="stat-value">1,245 kWh</div>
              <div class="stat-time">14:00-16:00</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon valley">
              <i class="fas fa-arrow-down"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">谷时用电</div>
              <div class="stat-value">456 kWh</div>
              <div class="stat-time">02:00-06:00</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 能效分析 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">能效分析</h3>
        <a href="#" class="link-text">详细报告</a>
      </div>
      <div class="efficiency-metrics">
        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-building"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">单位面积能耗</div>
            <div class="efficiency-value">
              <span class="value">45.6</span>
              <span class="unit">kWh/m²</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value negative">-3.2%</span>
            </div>
          </div>
        </div>

        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">人均能耗</div>
            <div class="efficiency-value">
              <span class="value">156.8</span>
              <span class="unit">kWh/人</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value positive">+1.5%</span>
            </div>
          </div>
        </div>

        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-industry"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">单位产值能耗</div>
            <div class="efficiency-value">
              <span class="value">0.85</span>
              <span class="unit">kWh/万元</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value negative">-5.8%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 节能潜力分析 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">节能潜力分析</h3>
        <button class="btn btn-primary btn-small">
          <i class="fas fa-lightbulb"></i>
          <span>查看建议</span>
        </button>
      </div>
      <div class="potential-analysis">
        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">错峰用电优化</div>
            <div class="potential-desc">调整生产时间至谷时段</div>
            <div class="potential-saving">预计节省: 15-20%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-warning">待实施</span>
          </div>
        </div>

        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-cog"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">设备运行优化</div>
            <div class="potential-desc">优化空调、照明设备运行策略</div>
            <div class="potential-saving">预计节省: 8-12%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-info">分析中</span>
          </div>
        </div>

        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-leaf"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">绿色能源替代</div>
            <div class="potential-desc">增加太阳能、风能等清洁能源</div>
            <div class="potential-saving">预计节省: 25-30%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-success">已规划</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    /* 高端时间选择器样式 */
    .time-selector-premium {
      background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
      border-radius: 24px;
      padding: 32px;
      border: 1px solid rgba(148, 163, 184, 0.1);
      box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
      position: relative;
      overflow: hidden;
    }

    .time-selector-premium::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
    }

    .selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 28px;
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .time-icon-wrapper {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      box-shadow: 0 8px 16px rgba(99, 102, 241, 0.3);
    }

    .header-text {
      flex: 1;
    }

    .selector-title {
      font-size: 20px;
      font-weight: 800;
      color: #1e293b;
      margin: 0 0 4px 0;
      letter-spacing: -0.025em;
    }

    .selector-subtitle {
      font-size: 14px;
      color: #64748b;
      margin: 0;
      font-weight: 500;
    }

    .custom-date-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
      color: #475569;
      border: 1px solid rgba(148, 163, 184, 0.2);
      border-radius: 16px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .custom-date-btn:hover {
      background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border-color: rgba(148, 163, 184, 0.3);
    }

    .custom-date-btn i {
      font-size: 12px;
    }

    .time-options-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
    }

    .time-option {
      background: linear-gradient(145deg, #ffffff, #f8fafc);
      border: 2px solid transparent;
      border-radius: 20px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .time-option::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .time-option:hover {
      transform: translateY(-4px);
      box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
      border-color: rgba(99, 102, 241, 0.2);
    }

    .time-option:hover::before {
      opacity: 1;
    }

    .time-option.active {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      color: white;
      border-color: #6366f1;
      box-shadow:
        0 20px 25px -5px rgba(99, 102, 241, 0.4),
        0 10px 10px -5px rgba(99, 102, 241, 0.2);
    }

    .option-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    .option-icon {
      width: 56px;
      height: 56px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
    }

    .option-icon.today {
      background: linear-gradient(135deg, #f59e0b, #f97316);
    }

    .option-icon.week {
      background: linear-gradient(135deg, #06b6d4, #0891b2);
    }

    .option-icon.month {
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }

    .option-icon.year {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    .time-option.active .option-icon {
      background: rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .option-badge {
      padding: 4px 12px;
      background: rgba(99, 102, 241, 0.1);
      color: #6366f1;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .time-option.active .option-badge {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }

    .option-content {
      margin-bottom: 16px;
    }

    .option-title {
      font-size: 20px;
      font-weight: 800;
      color: #1e293b;
      margin: 0 0 8px 0;
      letter-spacing: -0.025em;
    }

    .time-option.active .option-title {
      color: white;
    }

    .option-desc {
      font-size: 14px;
      color: #64748b;
      margin: 0 0 16px 0;
      font-weight: 500;
    }

    .time-option.active .option-desc {
      color: rgba(255, 255, 255, 0.8);
    }

    .option-stats {
      display: flex;
      align-items: baseline;
      gap: 8px;
    }

    .stats-value {
      font-size: 16px;
      font-weight: 700;
      color: #6366f1;
    }

    .time-option.active .stats-value {
      color: white;
    }

    .stats-label {
      font-size: 12px;
      color: #94a3b8;
      font-weight: 500;
    }

    .time-option.active .stats-label {
      color: rgba(255, 255, 255, 0.7);
    }

    .active-indicator {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #fbbf24, #f59e0b);
      border-radius: 0 0 18px 18px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .time-selector-premium {
        padding: 24px 20px;
      }

      .time-options-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .selector-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
        margin-bottom: 24px;
      }

      .header-left {
        gap: 12px;
      }

      .time-icon-wrapper {
        width: 40px;
        height: 40px;
        font-size: 18px;
      }

      .selector-title {
        font-size: 18px;
      }

      .custom-date-btn {
        justify-content: center;
        padding: 10px 16px;
      }
    }

    @media (max-width: 393px) {
      .time-selector-premium {
        padding: 20px 16px;
        border-radius: 20px;
      }

      .time-options-grid {
        gap: 12px;
      }

      .time-option {
        padding: 20px;
        border-radius: 16px;
      }

      .option-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
      }

      .option-title {
        font-size: 18px;
      }

      .option-desc {
        font-size: 13px;
      }

      .stats-value {
        font-size: 14px;
      }

      .stats-label {
        font-size: 11px;
      }
    }



    .chart-legend {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }

    .comparison-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
      backdrop-filter: blur(20px);
    }

    .chart-container {
      height: 160px;
      margin: 16px 0;
      position: relative;
    }

    .legend-color.current {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .legend-color.previous {
      background: linear-gradient(135deg, #bdc3c7, #95a5a6);
    }

    .comparison-summary {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 16px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid var(--border-light);
    }

    .summary-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    .summary-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .summary-item.electric .summary-icon {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .summary-item.water .summary-icon {
      background: linear-gradient(135deg, #11998e, #38ef7d);
    }

    .summary-item.gas .summary-icon {
      background: linear-gradient(135deg, #f093fb, #f5576c);
    }

    .summary-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }

    .summary-content {
      flex: 1;
    }

    .summary-label {
      display: block;
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
      font-weight: 500;
    }

    .summary-value {
      font-size: 16px;
      font-weight: 700;
    }

    .summary-value.positive {
      color: var(--success-green);
    }

    .summary-value.negative {
      color: var(--danger-red);
    }

    .ranking-tabs {
      display: flex;
      gap: 8px;
    }

    .ranking-tab {
      padding: 4px 12px;
      border: 1px solid var(--border-light);
      background: none;
      border-radius: 6px;
      color: var(--text-secondary);
      font-size: 12px;
      transition: all 0.2s;
    }

    .ranking-tab.active {
      background: var(--primary-blue);
      color: white;
      border-color: var(--primary-blue);
    }

    .ranking-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .ranking-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .ranking-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      color: white;
      background: var(--text-secondary);
    }

    .ranking-number.first { background: #FFD700; }
    .ranking-number.second { background: #C0C0C0; }
    .ranking-number.third { background: #CD7F32; }

    .ranking-info {
      flex: 1;
    }

    .ranking-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .ranking-detail {
      font-size: 11px;
      color: var(--text-secondary);
    }

    .ranking-value {
      text-align: right;
      margin-right: 8px;
    }

    .ranking-value .value {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .ranking-value .unit {
      font-size: 10px;
      color: var(--text-secondary);
    }

    .ranking-trend {
      display: flex;
      align-items: center;
      gap: 2px;
      font-size: 10px;
      font-weight: 600;
    }

    .trend-up { color: var(--success-green); }
    .trend-down { color: var(--danger-red); }
    .trend-stable { color: var(--text-secondary); }

    .peak-valley-analysis {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .peak-valley-chart {
      height: 100px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
    }

    .peak-valley-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .stat-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .stat-icon.peak { background: var(--danger-red); }
    .stat-icon.valley { background: var(--success-green); }

    .stat-info {
      flex: 1;
    }

    .stat-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 2px;
    }

    .stat-value {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .stat-time {
      font-size: 10px;
      color: var(--text-secondary);
    }

    .efficiency-metrics {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .efficiency-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .efficiency-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--primary-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }

    .efficiency-info {
      flex: 1;
    }

    .efficiency-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .efficiency-value {
      margin-bottom: 4px;
    }

    .efficiency-value .value {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .efficiency-value .unit {
      font-size: 12px;
      color: var(--text-secondary);
      margin-left: 4px;
    }

    .efficiency-change {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 11px;
    }

    .change-text {
      color: var(--text-secondary);
    }

    .change-value.positive {
      color: var(--success-green);
    }

    .change-value.negative {
      color: var(--danger-red);
    }

    .btn-small {
      padding: 6px 12px;
      font-size: 12px;
    }

    .potential-analysis {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .potential-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .potential-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--warning-orange);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .potential-content {
      flex: 1;
    }

    .potential-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .potential-desc {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .potential-saving {
      font-size: 11px;
      color: var(--success-green);
      font-weight: 600;
    }

    .potential-status {
      display: flex;
      align-items: center;
    }
  </style>

  <script>
    // 对比分析数据
    const comparisonData = {
      monthly: {
        labels: ['用电量', '用水量', '用气量', '供热量'],
        current: [12456, 8563, 4321, 2890],
        previous: [11823, 8742, 4244, 2756],
        currentLabel: '本月',
        previousLabel: '上月',
        changes: {
          electric: '+5.2%',
          water: '-2.1%',
          gas: '+1.8%'
        }
      },
      weekly: {
        labels: ['用电量', '用水量', '用气量', '供热量'],
        current: [2890, 1987, 1023, 678],
        previous: [2756, 2034, 998, 645],
        currentLabel: '本周',
        previousLabel: '上周',
        changes: {
          electric: '+4.9%',
          water: '-2.3%',
          gas: '+2.5%'
        }
      },
      target: {
        labels: ['用电量', '用水量', '用气量', '供热量'],
        current: [12456, 8563, 4321, 2890],
        previous: [12000, 8800, 4200, 2800],
        currentLabel: '实际值',
        previousLabel: '目标值',
        changes: {
          electric: '****%',
          water: '-2.7%',
          gas: '****%'
        }
      }
    };

    let comparisonChart;

    // 初始化对比图表
    function initComparisonChart() {
      const ctx = document.getElementById('comparisonChart').getContext('2d');

      comparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: comparisonData.monthly.labels,
          datasets: [
            {
              label: comparisonData.monthly.currentLabel,
              data: comparisonData.monthly.current,
              backgroundColor: 'rgba(102, 126, 234, 0.8)',
              borderColor: 'rgba(102, 126, 234, 1)',
              borderWidth: 2,
              borderRadius: 8,
              borderSkipped: false,
            },
            {
              label: comparisonData.monthly.previousLabel,
              data: comparisonData.monthly.previous,
              backgroundColor: 'rgba(189, 195, 199, 0.8)',
              borderColor: 'rgba(189, 195, 199, 1)',
              borderWidth: 2,
              borderRadius: 8,
              borderSkipped: false,
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#6B7280',
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            },
            y: {
              grid: {
                color: '#E5E7EB',
                lineWidth: 1
              },
              ticks: {
                color: '#6B7280',
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            }
          },
          elements: {
            bar: {
              borderRadius: 8
            }
          }
        }
      });
    }

    // 更新对比图表
    function updateComparisonChart(type) {
      const data = comparisonData[type];

      comparisonChart.data.labels = data.labels;
      comparisonChart.data.datasets[0].data = data.current;
      comparisonChart.data.datasets[1].data = data.previous;
      comparisonChart.data.datasets[0].label = data.currentLabel;
      comparisonChart.data.datasets[1].label = data.previousLabel;
      comparisonChart.update();

      // 更新图例
      document.getElementById('currentPeriod').textContent = data.currentLabel;
      document.getElementById('previousPeriod').textContent = data.previousLabel;

      // 更新变化数据
      document.getElementById('electricChange').textContent = data.changes.electric;
      document.getElementById('waterChange').textContent = data.changes.water;
      document.getElementById('gasChange').textContent = data.changes.gas;

      // 更新变化颜色
      updateChangeColors('electricChange', data.changes.electric);
      updateChangeColors('waterChange', data.changes.water);
      updateChangeColors('gasChange', data.changes.gas);
    }

    // 更新变化颜色
    function updateChangeColors(elementId, value) {
      const element = document.getElementById(elementId);
      element.className = 'summary-value ' + (value.startsWith('+') ? 'positive' : 'negative');
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      initComparisonChart();

      // 绑定对比类型选择器事件
      document.getElementById('comparisonType').addEventListener('change', function(e) {
        updateComparisonChart(e.target.value);
      });

      // 绑定时间选择器事件
      const timeOptions = document.querySelectorAll('.time-option');
      timeOptions.forEach(option => {
        option.addEventListener('click', function() {
          // 移除所有active类
          timeOptions.forEach(opt => {
            opt.classList.remove('active');
            // 移除活跃指示器
            const indicator = opt.querySelector('.active-indicator');
            if (indicator) {
              indicator.remove();
            }
          });

          // 添加active类到当前选中的选项
          this.classList.add('active');

          // 添加活跃指示器
          if (!this.querySelector('.active-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'active-indicator';
            this.appendChild(indicator);
          }

          // 获取选中的时间周期
          const period = this.getAttribute('data-period');
          console.log('选中时间周期:', period);

          // 这里可以添加更新图表数据的逻辑
          // updateChartsForPeriod(period);
        });
      });

      // 自定义时间按钮事件
      document.querySelector('.custom-date-btn').addEventListener('click', function() {
        // 这里可以打开日期选择器
        console.log('打开自定义时间选择器');
        // 可以集成第三方日期选择器库

        // 添加点击动画效果
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
          this.style.transform = '';
        }, 150);
      });
    });
  </script>
</body>
</html>

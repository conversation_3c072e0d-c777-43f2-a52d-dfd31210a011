<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据分析 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <div class="app-content">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">数据分析</h1>
      <div class="page-actions">
        <button class="btn-icon">
          <i class="fas fa-download"></i>
        </button>
        <button class="btn-icon">
          <i class="fas fa-share-alt"></i>
        </button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="time-selector mb-16">
      <div class="time-tabs">
        <button class="time-tab" data-period="day">今日</button>
        <button class="time-tab" data-period="week">本周</button>
        <button class="time-tab active" data-period="month">本月</button>
        <button class="time-tab" data-period="year">本年</button>
      </div>
      <button class="custom-time-btn">
        <i class="fas fa-calendar-alt"></i>
        <span>自定义</span>
      </button>
    </div>

    <!-- 能耗对比分析 -->
    <div class="card comparison-card mb-16">
      <div class="card-header">
        <h3 class="card-title">能耗对比分析</h3>
        <select class="select-small" id="comparisonType">
          <option value="monthly">同比分析</option>
          <option value="weekly">环比分析</option>
          <option value="target">目标对比</option>
        </select>
      </div>
      <div class="comparison-chart">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color current"></span>
            <span id="currentPeriod">本月</span>
          </div>
          <div class="legend-item">
            <span class="legend-color previous"></span>
            <span id="previousPeriod">上月</span>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="comparisonChart" width="320" height="160"></canvas>
        </div>
        <div class="comparison-summary">
          <div class="summary-item electric">
            <div class="summary-icon">
              <i class="fas fa-bolt"></i>
            </div>
            <div class="summary-content">
              <span class="summary-label">用电量变化</span>
              <span class="summary-value positive" id="electricChange">+5.2%</span>
            </div>
          </div>
          <div class="summary-item water">
            <div class="summary-icon">
              <i class="fas fa-tint"></i>
            </div>
            <div class="summary-content">
              <span class="summary-label">用水量变化</span>
              <span class="summary-value negative" id="waterChange">-2.1%</span>
            </div>
          </div>
          <div class="summary-item gas">
            <div class="summary-icon">
              <i class="fas fa-fire"></i>
            </div>
            <div class="summary-content">
              <span class="summary-label">用气量变化</span>
              <span class="summary-value positive" id="gasChange">+1.8%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 能耗排名 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">区域能耗排名</h3>
        <div class="ranking-tabs">
          <button class="ranking-tab active" data-type="electric">用电</button>
          <button class="ranking-tab" data-type="water">用水</button>
          <button class="ranking-tab" data-type="gas">用气</button>
        </div>
      </div>
      <div class="ranking-list">
        <div class="ranking-item">
          <div class="ranking-number first">1</div>
          <div class="ranking-info">
            <div class="ranking-name">B栋生产车间</div>
            <div class="ranking-detail">占总用电量 45.2%</div>
          </div>
          <div class="ranking-value">
            <span class="value">12,456</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-up trend-up"></i>
            <span>8.5%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number second">2</div>
          <div class="ranking-info">
            <div class="ranking-name">A栋办公楼</div>
            <div class="ranking-detail">占总用电量 32.1%</div>
          </div>
          <div class="ranking-value">
            <span class="value">8,845</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-down trend-down"></i>
            <span>2.3%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number third">3</div>
          <div class="ranking-info">
            <div class="ranking-name">C栋仓储区</div>
            <div class="ranking-detail">占总用电量 22.7%</div>
          </div>
          <div class="ranking-value">
            <span class="value">6,234</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-up trend-up"></i>
            <span>1.2%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number">4</div>
          <div class="ranking-info">
            <div class="ranking-name">D栋研发中心</div>
            <div class="ranking-detail">占总用电量 15.6%</div>
          </div>
          <div class="ranking-value">
            <span class="value">4,298</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-minus trend-stable"></i>
            <span>0.1%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 峰谷分析 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">用电峰谷分析</h3>
        <select class="select-small">
          <option>本周</option>
          <option>本月</option>
          <option>本年</option>
        </select>
      </div>
      <div class="peak-valley-analysis">
        <div class="peak-valley-chart">
          <canvas id="peakValleyChart" width="300" height="100"></canvas>
        </div>
        <div class="peak-valley-stats">
          <div class="stat-item">
            <div class="stat-icon peak">
              <i class="fas fa-arrow-up"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">峰时用电</div>
              <div class="stat-value">1,245 kWh</div>
              <div class="stat-time">14:00-16:00</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon valley">
              <i class="fas fa-arrow-down"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">谷时用电</div>
              <div class="stat-value">456 kWh</div>
              <div class="stat-time">02:00-06:00</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 能效分析 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">能效分析</h3>
        <a href="#" class="link-text">详细报告</a>
      </div>
      <div class="efficiency-metrics">
        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-building"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">单位面积能耗</div>
            <div class="efficiency-value">
              <span class="value">45.6</span>
              <span class="unit">kWh/m²</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value negative">-3.2%</span>
            </div>
          </div>
        </div>

        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">人均能耗</div>
            <div class="efficiency-value">
              <span class="value">156.8</span>
              <span class="unit">kWh/人</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value positive">+1.5%</span>
            </div>
          </div>
        </div>

        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-industry"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">单位产值能耗</div>
            <div class="efficiency-value">
              <span class="value">0.85</span>
              <span class="unit">kWh/万元</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value negative">-5.8%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 节能潜力分析 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">节能潜力分析</h3>
        <button class="btn btn-primary btn-small">
          <i class="fas fa-lightbulb"></i>
          <span>查看建议</span>
        </button>
      </div>
      <div class="potential-analysis">
        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">错峰用电优化</div>
            <div class="potential-desc">调整生产时间至谷时段</div>
            <div class="potential-saving">预计节省: 15-20%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-warning">待实施</span>
          </div>
        </div>

        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-cog"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">设备运行优化</div>
            <div class="potential-desc">优化空调、照明设备运行策略</div>
            <div class="potential-saving">预计节省: 8-12%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-info">分析中</span>
          </div>
        </div>

        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-leaf"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">绿色能源替代</div>
            <div class="potential-desc">增加太阳能、风能等清洁能源</div>
            <div class="potential-saving">预计节省: 25-30%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-success">已规划</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .time-selector {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: var(--card-white);
      border-radius: 12px;
      padding: 8px;
    }

    .time-tabs {
      display: flex;
      gap: 4px;
    }

    .time-tab {
      padding: 8px 16px;
      border: none;
      background: none;
      border-radius: 8px;
      color: var(--text-secondary);
      font-size: 14px;
      transition: all 0.2s;
    }

    .time-tab.active {
      background: var(--primary-blue);
      color: white;
    }

    .custom-time-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 12px;
      border: 1px solid var(--border-light);
      background: none;
      border-radius: 8px;
      color: var(--text-secondary);
      font-size: 12px;
    }

    .chart-legend {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }

    .comparison-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
      backdrop-filter: blur(20px);
    }

    .chart-container {
      height: 160px;
      margin: 16px 0;
      position: relative;
    }

    .legend-color.current {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .legend-color.previous {
      background: linear-gradient(135deg, #bdc3c7, #95a5a6);
    }

    .comparison-summary {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 16px;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid var(--border-light);
    }

    .summary-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }

    .summary-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .summary-item.electric .summary-icon {
      background: linear-gradient(135deg, #667eea, #764ba2);
    }

    .summary-item.water .summary-icon {
      background: linear-gradient(135deg, #11998e, #38ef7d);
    }

    .summary-item.gas .summary-icon {
      background: linear-gradient(135deg, #f093fb, #f5576c);
    }

    .summary-icon {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }

    .summary-content {
      flex: 1;
    }

    .summary-label {
      display: block;
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
      font-weight: 500;
    }

    .summary-value {
      font-size: 16px;
      font-weight: 700;
    }

    .summary-value.positive {
      color: var(--success-green);
    }

    .summary-value.negative {
      color: var(--danger-red);
    }

    .ranking-tabs {
      display: flex;
      gap: 8px;
    }

    .ranking-tab {
      padding: 4px 12px;
      border: 1px solid var(--border-light);
      background: none;
      border-radius: 6px;
      color: var(--text-secondary);
      font-size: 12px;
      transition: all 0.2s;
    }

    .ranking-tab.active {
      background: var(--primary-blue);
      color: white;
      border-color: var(--primary-blue);
    }

    .ranking-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .ranking-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .ranking-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      color: white;
      background: var(--text-secondary);
    }

    .ranking-number.first { background: #FFD700; }
    .ranking-number.second { background: #C0C0C0; }
    .ranking-number.third { background: #CD7F32; }

    .ranking-info {
      flex: 1;
    }

    .ranking-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .ranking-detail {
      font-size: 11px;
      color: var(--text-secondary);
    }

    .ranking-value {
      text-align: right;
      margin-right: 8px;
    }

    .ranking-value .value {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .ranking-value .unit {
      font-size: 10px;
      color: var(--text-secondary);
    }

    .ranking-trend {
      display: flex;
      align-items: center;
      gap: 2px;
      font-size: 10px;
      font-weight: 600;
    }

    .trend-up { color: var(--success-green); }
    .trend-down { color: var(--danger-red); }
    .trend-stable { color: var(--text-secondary); }

    .peak-valley-analysis {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .peak-valley-chart {
      height: 100px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
    }

    .peak-valley-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .stat-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .stat-icon.peak { background: var(--danger-red); }
    .stat-icon.valley { background: var(--success-green); }

    .stat-info {
      flex: 1;
    }

    .stat-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 2px;
    }

    .stat-value {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .stat-time {
      font-size: 10px;
      color: var(--text-secondary);
    }

    .efficiency-metrics {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .efficiency-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .efficiency-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--primary-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }

    .efficiency-info {
      flex: 1;
    }

    .efficiency-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .efficiency-value {
      margin-bottom: 4px;
    }

    .efficiency-value .value {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .efficiency-value .unit {
      font-size: 12px;
      color: var(--text-secondary);
      margin-left: 4px;
    }

    .efficiency-change {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 11px;
    }

    .change-text {
      color: var(--text-secondary);
    }

    .change-value.positive {
      color: var(--success-green);
    }

    .change-value.negative {
      color: var(--danger-red);
    }

    .btn-small {
      padding: 6px 12px;
      font-size: 12px;
    }

    .potential-analysis {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .potential-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .potential-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--warning-orange);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .potential-content {
      flex: 1;
    }

    .potential-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .potential-desc {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .potential-saving {
      font-size: 11px;
      color: var(--success-green);
      font-weight: 600;
    }

    .potential-status {
      display: flex;
      align-items: center;
    }
  </style>

  <script>
    // 对比分析数据
    const comparisonData = {
      monthly: {
        labels: ['用电量', '用水量', '用气量', '供热量'],
        current: [12456, 8563, 4321, 2890],
        previous: [11823, 8742, 4244, 2756],
        currentLabel: '本月',
        previousLabel: '上月',
        changes: {
          electric: '+5.2%',
          water: '-2.1%',
          gas: '+1.8%'
        }
      },
      weekly: {
        labels: ['用电量', '用水量', '用气量', '供热量'],
        current: [2890, 1987, 1023, 678],
        previous: [2756, 2034, 998, 645],
        currentLabel: '本周',
        previousLabel: '上周',
        changes: {
          electric: '+4.9%',
          water: '-2.3%',
          gas: '+2.5%'
        }
      },
      target: {
        labels: ['用电量', '用水量', '用气量', '供热量'],
        current: [12456, 8563, 4321, 2890],
        previous: [12000, 8800, 4200, 2800],
        currentLabel: '实际值',
        previousLabel: '目标值',
        changes: {
          electric: '****%',
          water: '-2.7%',
          gas: '****%'
        }
      }
    };

    let comparisonChart;

    // 初始化对比图表
    function initComparisonChart() {
      const ctx = document.getElementById('comparisonChart').getContext('2d');

      comparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: comparisonData.monthly.labels,
          datasets: [
            {
              label: comparisonData.monthly.currentLabel,
              data: comparisonData.monthly.current,
              backgroundColor: 'rgba(102, 126, 234, 0.8)',
              borderColor: 'rgba(102, 126, 234, 1)',
              borderWidth: 2,
              borderRadius: 8,
              borderSkipped: false,
            },
            {
              label: comparisonData.monthly.previousLabel,
              data: comparisonData.monthly.previous,
              backgroundColor: 'rgba(189, 195, 199, 0.8)',
              borderColor: 'rgba(189, 195, 199, 1)',
              borderWidth: 2,
              borderRadius: 8,
              borderSkipped: false,
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#6B7280',
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            },
            y: {
              grid: {
                color: '#E5E7EB',
                lineWidth: 1
              },
              ticks: {
                color: '#6B7280',
                font: {
                  size: 12,
                  weight: '500'
                }
              }
            }
          },
          elements: {
            bar: {
              borderRadius: 8
            }
          }
        }
      });
    }

    // 更新对比图表
    function updateComparisonChart(type) {
      const data = comparisonData[type];

      comparisonChart.data.labels = data.labels;
      comparisonChart.data.datasets[0].data = data.current;
      comparisonChart.data.datasets[1].data = data.previous;
      comparisonChart.data.datasets[0].label = data.currentLabel;
      comparisonChart.data.datasets[1].label = data.previousLabel;
      comparisonChart.update();

      // 更新图例
      document.getElementById('currentPeriod').textContent = data.currentLabel;
      document.getElementById('previousPeriod').textContent = data.previousLabel;

      // 更新变化数据
      document.getElementById('electricChange').textContent = data.changes.electric;
      document.getElementById('waterChange').textContent = data.changes.water;
      document.getElementById('gasChange').textContent = data.changes.gas;

      // 更新变化颜色
      updateChangeColors('electricChange', data.changes.electric);
      updateChangeColors('waterChange', data.changes.water);
      updateChangeColors('gasChange', data.changes.gas);
    }

    // 更新变化颜色
    function updateChangeColors(elementId, value) {
      const element = document.getElementById(elementId);
      element.className = 'summary-value ' + (value.startsWith('+') ? 'positive' : 'negative');
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      initComparisonChart();

      // 绑定对比类型选择器事件
      document.getElementById('comparisonType').addEventListener('change', function(e) {
        updateComparisonChart(e.target.value);
      });
    });
  </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据分析 - 能源管理</title>
  <link rel="stylesheet" href="../styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="app-content">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">数据分析</h1>
      <div class="page-actions">
        <button class="btn-icon">
          <i class="fas fa-download"></i>
        </button>
        <button class="btn-icon">
          <i class="fas fa-share-alt"></i>
        </button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="time-selector mb-16">
      <div class="time-tabs">
        <button class="time-tab" data-period="day">今日</button>
        <button class="time-tab" data-period="week">本周</button>
        <button class="time-tab active" data-period="month">本月</button>
        <button class="time-tab" data-period="year">本年</button>
      </div>
      <button class="custom-time-btn">
        <i class="fas fa-calendar-alt"></i>
        <span>自定义</span>
      </button>
    </div>

    <!-- 能耗对比分析 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">能耗对比分析</h3>
        <select class="select-small">
          <option>同比分析</option>
          <option>环比分析</option>
          <option>目标对比</option>
        </select>
      </div>
      <div class="comparison-chart">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color" style="background: var(--primary-blue);"></span>
            <span>本月</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background: var(--text-secondary);"></span>
            <span>上月</span>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="comparisonChart" width="300" height="120"></canvas>
        </div>
        <div class="comparison-summary">
          <div class="summary-item">
            <span class="summary-label">用电量变化</span>
            <span class="summary-value positive">+5.2%</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">用水量变化</span>
            <span class="summary-value negative">-2.1%</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">用气量变化</span>
            <span class="summary-value positive">+1.8%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 能耗排名 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">区域能耗排名</h3>
        <div class="ranking-tabs">
          <button class="ranking-tab active" data-type="electric">用电</button>
          <button class="ranking-tab" data-type="water">用水</button>
          <button class="ranking-tab" data-type="gas">用气</button>
        </div>
      </div>
      <div class="ranking-list">
        <div class="ranking-item">
          <div class="ranking-number first">1</div>
          <div class="ranking-info">
            <div class="ranking-name">B栋生产车间</div>
            <div class="ranking-detail">占总用电量 45.2%</div>
          </div>
          <div class="ranking-value">
            <span class="value">12,456</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-up trend-up"></i>
            <span>8.5%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number second">2</div>
          <div class="ranking-info">
            <div class="ranking-name">A栋办公楼</div>
            <div class="ranking-detail">占总用电量 32.1%</div>
          </div>
          <div class="ranking-value">
            <span class="value">8,845</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-down trend-down"></i>
            <span>2.3%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number third">3</div>
          <div class="ranking-info">
            <div class="ranking-name">C栋仓储区</div>
            <div class="ranking-detail">占总用电量 22.7%</div>
          </div>
          <div class="ranking-value">
            <span class="value">6,234</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-arrow-up trend-up"></i>
            <span>1.2%</span>
          </div>
        </div>

        <div class="ranking-item">
          <div class="ranking-number">4</div>
          <div class="ranking-info">
            <div class="ranking-name">D栋研发中心</div>
            <div class="ranking-detail">占总用电量 15.6%</div>
          </div>
          <div class="ranking-value">
            <span class="value">4,298</span>
            <span class="unit">kWh</span>
          </div>
          <div class="ranking-trend">
            <i class="fas fa-minus trend-stable"></i>
            <span>0.1%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 峰谷分析 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">用电峰谷分析</h3>
        <select class="select-small">
          <option>本周</option>
          <option>本月</option>
          <option>本年</option>
        </select>
      </div>
      <div class="peak-valley-analysis">
        <div class="peak-valley-chart">
          <canvas id="peakValleyChart" width="300" height="100"></canvas>
        </div>
        <div class="peak-valley-stats">
          <div class="stat-item">
            <div class="stat-icon peak">
              <i class="fas fa-arrow-up"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">峰时用电</div>
              <div class="stat-value">1,245 kWh</div>
              <div class="stat-time">14:00-16:00</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon valley">
              <i class="fas fa-arrow-down"></i>
            </div>
            <div class="stat-info">
              <div class="stat-label">谷时用电</div>
              <div class="stat-value">456 kWh</div>
              <div class="stat-time">02:00-06:00</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 能效分析 -->
    <div class="card mb-16">
      <div class="card-header">
        <h3 class="card-title">能效分析</h3>
        <a href="#" class="link-text">详细报告</a>
      </div>
      <div class="efficiency-metrics">
        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-building"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">单位面积能耗</div>
            <div class="efficiency-value">
              <span class="value">45.6</span>
              <span class="unit">kWh/m²</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value negative">-3.2%</span>
            </div>
          </div>
        </div>

        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">人均能耗</div>
            <div class="efficiency-value">
              <span class="value">156.8</span>
              <span class="unit">kWh/人</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value positive">+1.5%</span>
            </div>
          </div>
        </div>

        <div class="efficiency-item">
          <div class="efficiency-icon">
            <i class="fas fa-industry"></i>
          </div>
          <div class="efficiency-info">
            <div class="efficiency-label">单位产值能耗</div>
            <div class="efficiency-value">
              <span class="value">0.85</span>
              <span class="unit">kWh/万元</span>
            </div>
            <div class="efficiency-change">
              <span class="change-text">较上月</span>
              <span class="change-value negative">-5.8%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 节能潜力分析 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">节能潜力分析</h3>
        <button class="btn btn-primary btn-small">
          <i class="fas fa-lightbulb"></i>
          <span>查看建议</span>
        </button>
      </div>
      <div class="potential-analysis">
        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">错峰用电优化</div>
            <div class="potential-desc">调整生产时间至谷时段</div>
            <div class="potential-saving">预计节省: 15-20%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-warning">待实施</span>
          </div>
        </div>

        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-cog"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">设备运行优化</div>
            <div class="potential-desc">优化空调、照明设备运行策略</div>
            <div class="potential-saving">预计节省: 8-12%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-info">分析中</span>
          </div>
        </div>

        <div class="potential-item">
          <div class="potential-icon">
            <i class="fas fa-leaf"></i>
          </div>
          <div class="potential-content">
            <div class="potential-title">绿色能源替代</div>
            <div class="potential-desc">增加太阳能、风能等清洁能源</div>
            <div class="potential-saving">预计节省: 25-30%</div>
          </div>
          <div class="potential-status">
            <span class="status-badge status-success">已规划</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    .time-selector {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: var(--card-white);
      border-radius: 12px;
      padding: 8px;
    }

    .time-tabs {
      display: flex;
      gap: 4px;
    }

    .time-tab {
      padding: 8px 16px;
      border: none;
      background: none;
      border-radius: 8px;
      color: var(--text-secondary);
      font-size: 14px;
      transition: all 0.2s;
    }

    .time-tab.active {
      background: var(--primary-blue);
      color: white;
    }

    .custom-time-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 12px;
      border: 1px solid var(--border-light);
      background: none;
      border-radius: 8px;
      color: var(--text-secondary);
      font-size: 12px;
    }

    .chart-legend {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
    }

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }

    .chart-container {
      height: 120px;
      background: #f8f9fa;
      border-radius: 8px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
    }

    .comparison-summary {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 12px;
    }

    .summary-item {
      text-align: center;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 6px;
    }

    .summary-label {
      display: block;
      font-size: 11px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .summary-value {
      font-size: 14px;
      font-weight: 600;
    }

    .summary-value.positive {
      color: var(--success-green);
    }

    .summary-value.negative {
      color: var(--danger-red);
    }

    .ranking-tabs {
      display: flex;
      gap: 8px;
    }

    .ranking-tab {
      padding: 4px 12px;
      border: 1px solid var(--border-light);
      background: none;
      border-radius: 6px;
      color: var(--text-secondary);
      font-size: 12px;
      transition: all 0.2s;
    }

    .ranking-tab.active {
      background: var(--primary-blue);
      color: white;
      border-color: var(--primary-blue);
    }

    .ranking-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .ranking-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .ranking-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      color: white;
      background: var(--text-secondary);
    }

    .ranking-number.first { background: #FFD700; }
    .ranking-number.second { background: #C0C0C0; }
    .ranking-number.third { background: #CD7F32; }

    .ranking-info {
      flex: 1;
    }

    .ranking-name {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .ranking-detail {
      font-size: 11px;
      color: var(--text-secondary);
    }

    .ranking-value {
      text-align: right;
      margin-right: 8px;
    }

    .ranking-value .value {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .ranking-value .unit {
      font-size: 10px;
      color: var(--text-secondary);
    }

    .ranking-trend {
      display: flex;
      align-items: center;
      gap: 2px;
      font-size: 10px;
      font-weight: 600;
    }

    .trend-up { color: var(--success-green); }
    .trend-down { color: var(--danger-red); }
    .trend-stable { color: var(--text-secondary); }

    .peak-valley-analysis {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .peak-valley-chart {
      height: 100px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);
    }

    .peak-valley-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .stat-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .stat-icon.peak { background: var(--danger-red); }
    .stat-icon.valley { background: var(--success-green); }

    .stat-info {
      flex: 1;
    }

    .stat-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 2px;
    }

    .stat-value {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .stat-time {
      font-size: 10px;
      color: var(--text-secondary);
    }

    .efficiency-metrics {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .efficiency-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .efficiency-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: var(--primary-blue);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
    }

    .efficiency-info {
      flex: 1;
    }

    .efficiency-label {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .efficiency-value {
      margin-bottom: 4px;
    }

    .efficiency-value .value {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .efficiency-value .unit {
      font-size: 12px;
      color: var(--text-secondary);
      margin-left: 4px;
    }

    .efficiency-change {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 11px;
    }

    .change-text {
      color: var(--text-secondary);
    }

    .change-value.positive {
      color: var(--success-green);
    }

    .change-value.negative {
      color: var(--danger-red);
    }

    .btn-small {
      padding: 6px 12px;
      font-size: 12px;
    }

    .potential-analysis {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .potential-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .potential-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--warning-orange);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
    }

    .potential-content {
      flex: 1;
    }

    .potential-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .potential-desc {
      font-size: 12px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }

    .potential-saving {
      font-size: 11px;
      color: var(--success-green);
      font-weight: 600;
    }

    .potential-status {
      display: flex;
      align-items: center;
    }
  </style>
</body>
</html>

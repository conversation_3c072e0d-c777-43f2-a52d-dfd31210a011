<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>能源管理系统APP - 高保真原型</title>
  <link rel="stylesheet" href="styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    body {
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .prototype-header {
      text-align: center;
      color: white;
      margin-bottom: 30px;
    }

    .prototype-title {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .prototype-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin-bottom: 20px;
    }

    .prototype-info {
      display: flex;
      justify-content: center;
      gap: 30px;
      font-size: 14px;
      opacity: 0.8;
    }

    .devices-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .device-frame {
      width: 393px;
      height: 852px;
      background: #000;
      border-radius: 20px;
      padding: 2px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      position: relative;
      overflow: hidden;
    }

    .device-screen {
      width: 100%;
      height: 100%;
      background: var(--background-gray);
      border-radius: 18px;
      overflow: hidden;
      position: relative;
    }

    .device-label {
      position: absolute;
      top: -30px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      white-space: nowrap;
    }

    .status-bar {
      height: 44px;
      background: var(--card-white);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      font-size: 14px;
      font-weight: 600;
    }

    .status-left {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .status-right {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .page-iframe {
      width: 100%;
      height: calc(100% - 44px - 83px);
      border: none;
      background: var(--background-gray);
    }

    .tab-bar {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 83px;
      background: var(--card-white);
      border-top: 1px solid var(--border-light);
      display: flex;
      justify-content: space-around;
      align-items: flex-start;
      padding-top: 8px;
    }

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      text-decoration: none;
      color: var(--text-secondary);
      transition: color 0.2s;
      cursor: pointer;
    }

    .tab-item.active {
      color: var(--primary-blue);
    }

    .tab-icon {
      font-size: 24px;
    }

    .tab-label {
      font-size: 10px;
      font-weight: 500;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 40px;
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
    }

    .feature-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px;
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .feature-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .feature-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .feature-list li {
      padding: 4px 0;
      font-size: 14px;
      opacity: 0.9;
    }

    .feature-list li::before {
      content: '✓';
      color: #4ade80;
      font-weight: bold;
      margin-right: 8px;
    }

    @media (max-width: 768px) {
      .devices-container {
        flex-direction: column;
        align-items: center;
      }
      
      .device-frame {
        transform: scale(0.8);
      }
      
      .prototype-info {
        flex-direction: column;
        gap: 10px;
      }
    }
  </style>
</head>
<body>
  <!-- 原型介绍 -->
  <div class="prototype-header">
    <h1 class="prototype-title">能源管理系统APP</h1>
    <p class="prototype-subtitle">高保真交互原型 - iPhone 15 Pro 设计</p>
    <div class="prototype-info">
      <span><i class="fas fa-mobile-alt"></i> 设备尺寸: 393×852px</span>
      <span><i class="fas fa-palette"></i> iOS 设计规范</span>
      <span><i class="fas fa-code"></i> HTML + CSS + JavaScript</span>
    </div>
  </div>

  <!-- 设备展示区域 -->
  <div class="devices-container">
    <!-- 首页设备 -->
    <div class="device-frame">
      <div class="device-label">首页 - 能源总览</div>
      <div class="device-screen">
        <!-- iOS状态栏 -->
        <div class="status-bar">
          <div class="status-left">
            <span>9:41</span>
          </div>
          <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>

        <!-- 页面内容 -->
        <iframe src="pages/home.html" class="page-iframe"></iframe>

        <!-- 底部Tab栏 -->
        <div class="tab-bar">
          <div class="tab-item active">
            <i class="fas fa-home tab-icon"></i>
            <span class="tab-label">首页</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span class="tab-label">监控</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-bar tab-icon"></i>
            <span class="tab-label">分析</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-cog tab-icon"></i>
            <span class="tab-label">管理</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span class="tab-label">我的</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时监控设备 -->
    <div class="device-frame">
      <div class="device-label">实时监控</div>
      <div class="device-screen">
        <div class="status-bar">
          <div class="status-left">
            <span>9:41</span>
          </div>
          <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <iframe src="pages/monitor.html" class="page-iframe"></iframe>
        <div class="tab-bar">
          <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span class="tab-label">首页</span>
          </div>
          <div class="tab-item active">
            <i class="fas fa-chart-line tab-icon"></i>
            <span class="tab-label">监控</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-bar tab-icon"></i>
            <span class="tab-label">分析</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-cog tab-icon"></i>
            <span class="tab-label">管理</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span class="tab-label">我的</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据分析设备 -->
    <div class="device-frame">
      <div class="device-label">数据分析</div>
      <div class="device-screen">
        <div class="status-bar">
          <div class="status-left">
            <span>9:41</span>
          </div>
          <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <iframe src="pages/analytics.html" class="page-iframe"></iframe>
        <div class="tab-bar">
          <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span class="tab-label">首页</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span class="tab-label">监控</span>
          </div>
          <div class="tab-item active">
            <i class="fas fa-chart-bar tab-icon"></i>
            <span class="tab-label">分析</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-cog tab-icon"></i>
            <span class="tab-label">管理</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span class="tab-label">我的</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 管理中心设备 -->
    <div class="device-frame">
      <div class="device-label">管理中心</div>
      <div class="device-screen">
        <div class="status-bar">
          <div class="status-left">
            <span>9:41</span>
          </div>
          <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <iframe src="pages/manage.html" class="page-iframe"></iframe>
        <div class="tab-bar">
          <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span class="tab-label">首页</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span class="tab-label">监控</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-bar tab-icon"></i>
            <span class="tab-label">分析</span>
          </div>
          <div class="tab-item active">
            <i class="fas fa-cog tab-icon"></i>
            <span class="tab-label">管理</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-user tab-icon"></i>
            <span class="tab-label">我的</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心设备 -->
    <div class="device-frame">
      <div class="device-label">个人中心</div>
      <div class="device-screen">
        <div class="status-bar">
          <div class="status-left">
            <span>9:41</span>
          </div>
          <div class="status-right">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <iframe src="pages/profile.html" class="page-iframe"></iframe>
        <div class="tab-bar">
          <div class="tab-item">
            <i class="fas fa-home tab-icon"></i>
            <span class="tab-label">首页</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-line tab-icon"></i>
            <span class="tab-label">监控</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-chart-bar tab-icon"></i>
            <span class="tab-label">分析</span>
          </div>
          <div class="tab-item">
            <i class="fas fa-cog tab-icon"></i>
            <span class="tab-label">管理</span>
          </div>
          <div class="tab-item active">
            <i class="fas fa-user tab-icon"></i>
            <span class="tab-label">我的</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 功能特性介绍 -->
  <div class="features-grid">
    <div class="feature-card">
      <h3 class="feature-title">
        <i class="fas fa-chart-line"></i>
        全景监测与数据采集
      </h3>
      <ul class="feature-list">
        <li>多能源接入（电、水、气、热、冷）</li>
        <li>多层级分项计量</li>
        <li>多协议设备接入</li>
        <li>实时监控仪表</li>
      </ul>
    </div>

    <div class="feature-card">
      <h3 class="feature-title">
        <i class="fas fa-chart-bar"></i>
        数据可视化与分析
      </h3>
      <ul class="feature-list">
        <li>能耗一张图总览</li>
        <li>GIS/三维地图展现</li>
        <li>多维用能分析</li>
        <li>趋势对比分析</li>
      </ul>
    </div>

    <div class="feature-card">
      <h3 class="feature-title">
        <i class="fas fa-bell"></i>
        智能告警与预警
      </h3>
      <ul class="feature-list">
        <li>基于阈值的告警机制</li>
        <li>多渠道告警通知</li>
        <li>故障根因分析</li>
        <li>告警规则配置</li>
      </ul>
    </div>

    <div class="feature-card">
      <h3 class="feature-title">
        <i class="fas fa-cog"></i>
        节能控制与策略
      </h3>
      <ul class="feature-list">
        <li>智能调度与控制</li>
        <li>节能策略执行</li>
        <li>能效评估分析</li>
        <li>碳排放管理</li>
      </ul>
    </div>

    <div class="feature-card">
      <h3 class="feature-title">
        <i class="fas fa-file-alt"></i>
        报表服务与决策
      </h3>
      <ul class="feature-list">
        <li>自定义报表导出</li>
        <li>多维度分析报告</li>
        <li>驾驶舱大屏展示</li>
        <li>决策支持系统</li>
      </ul>
    </div>

    <div class="feature-card">
      <h3 class="feature-title">
        <i class="fas fa-tools"></i>
        设备与运维管理
      </h3>
      <ul class="feature-list">
        <li>设备台账管理</li>
        <li>巡检工单管理</li>
        <li>操作审计管理</li>
        <li>权限角色管理</li>
      </ul>
    </div>
  </div>
</body>
</html>

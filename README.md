# 能源管理系统APP - 高保真原型

## 项目概述

这是一个完整的能源管理系统APP高保真原型，采用现代化的iOS设计规范，模拟iPhone 15 Pro的界面效果。原型涵盖了能源管理的核心功能模块，可直接用于产品开发参考。

## 功能特性

### 🏠 首页仪表盘
- 能耗总览数据展示
- 关键指标卡片
- 7日能耗趋势图表
- 设备状态概览
- 最新告警信息
- 快捷操作入口

### 📊 实时监控
- 多能源类型切换（电力、水务、燃气、供热）
- 实时数据仪表盘
- 区域监控列表
- 设备状态监控
- 实时告警推送

### 📈 数据分析
- 时间范围选择器
- 能耗对比分析（同比、环比）
- 区域能耗排名
- 峰谷用电分析
- 能效指标分析
- 节能潜力识别

### ⚙️ 管理中心
- 告警管理（活跃告警、告警统计、规则配置）
- 设备管理入口
- 报表中心入口
- 节能控制入口
- 碳资产管理入口
- 运维管理入口

### 👤 个人中心
- 用户信息展示
- 快捷功能入口
- 系统设置（通知、主题、语言等）
- 账户管理
- 应用信息
- 帮助与支持

## 技术实现

### 设计规范
- **设备尺寸**: iPhone 15 Pro (393×852px)
- **设计语言**: iOS Human Interface Guidelines
- **圆角设计**: 20px 设备外框，12px 卡片圆角
- **颜色系统**: 
  - 主色调: #007AFF (iOS蓝)
  - 成功色: #34C759 (绿色)
  - 警告色: #FF9500 (橙色)
  - 危险色: #FF3B30 (红色)

### 技术栈
- **HTML5**: 语义化标签，响应式布局
- **CSS3**: Flexbox/Grid布局，CSS变量，动画效果
- **Font Awesome 6.0**: 图标库
- **响应式设计**: 支持不同屏幕尺寸

### 文件结构
```
能源管理APP/
├── index.html              # 主入口页面
├── styles/
│   └── common.css          # 通用样式文件
├── components/
│   └── common.html         # 公共组件模板
├── pages/
│   ├── home.html          # 首页仪表盘
│   ├── monitor.html       # 实时监控
│   ├── analytics.html     # 数据分析
│   ├── manage.html        # 管理中心
│   └── profile.html       # 个人中心
└── README.md              # 项目说明文档
```

## 使用方法

### 本地预览
1. 下载所有文件到本地目录
2. 使用现代浏览器打开 `index.html`
3. 推荐使用Chrome、Safari、Firefox等浏览器

### 在线部署
1. 将所有文件上传到Web服务器
2. 确保文件路径结构保持不变
3. 通过域名访问index.html

### 开发集成
1. 各页面文件可独立使用
2. 复制相关CSS样式到项目中
3. 根据实际需求调整颜色、尺寸等参数

## 界面展示

原型包含5个主要界面，每个界面都以iPhone 15 Pro的形式展示：

1. **首页** - 展示能耗总览和关键指标
2. **监控** - 实时数据监控和设备状态
3. **分析** - 多维度数据分析和趋势对比
4. **管理** - 告警管理和系统配置
5. **个人** - 用户中心和系统设置

## 设计亮点

### 🎨 视觉设计
- 采用iOS原生设计语言
- 现代化的卡片式布局
- 清晰的信息层级
- 一致的交互反馈

### 📱 用户体验
- 符合移动端操作习惯
- 直观的导航结构
- 高效的信息展示
- 友好的错误提示

### 🔧 技术特性
- 响应式布局设计
- 模块化组件结构
- 可维护的代码架构
- 跨浏览器兼容性

## 适用场景

### 产品设计
- UI/UX设计参考
- 交互流程设计
- 视觉规范制定

### 开发实现
- 前端开发参考
- 组件库构建
- 样式系统设计

### 业务展示
- 客户演示
- 需求沟通
- 产品宣传

## 扩展建议

### 功能扩展
- 添加更多图表类型
- 增加数据导出功能
- 集成地图组件
- 添加消息推送

### 技术优化
- 引入JavaScript交互
- 添加数据模拟
- 优化加载性能
- 增加动画效果

### 设计优化
- 支持深色模式
- 增加多语言支持
- 优化无障碍访问
- 适配更多设备尺寸

## 版本信息

- **版本**: v1.0.0
- **创建日期**: 2024-01-15
- **设计规范**: iOS Human Interface Guidelines
- **兼容性**: 现代浏览器 (Chrome 80+, Safari 13+, Firefox 75+)

## 联系方式

如有任何问题或建议，欢迎联系：
- 邮箱: <EMAIL>
- 电话: 400-123-4567

---

*本原型仅供设计和开发参考使用，实际产品功能以最终版本为准。*

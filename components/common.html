<!-- 公共组件模板 -->

<!-- iOS状态栏 -->
<div class="status-bar">
  <div class="status-left">
    <span>9:41</span>
  </div>
  <div class="status-right">
    <i class="fas fa-signal"></i>
    <i class="fas fa-wifi"></i>
    <i class="fas fa-battery-three-quarters"></i>
  </div>
</div>

<!-- 底部Tab栏 -->
<div class="tab-bar">
  <a href="#" class="tab-item active" data-page="home">
    <i class="fas fa-home tab-icon"></i>
    <span class="tab-label">首页</span>
  </a>
  <a href="#" class="tab-item" data-page="monitor">
    <i class="fas fa-chart-line tab-icon"></i>
    <span class="tab-label">监控</span>
  </a>
  <a href="#" class="tab-item" data-page="analytics">
    <i class="fas fa-chart-bar tab-icon"></i>
    <span class="tab-label">分析</span>
  </a>
  <a href="#" class="tab-item" data-page="manage">
    <i class="fas fa-cog tab-icon"></i>
    <span class="tab-label">管理</span>
  </a>
  <a href="#" class="tab-item" data-page="profile">
    <i class="fas fa-user tab-icon"></i>
    <span class="tab-label">我的</span>
  </a>
</div>

<!-- 页面头部模板 -->
<div class="page-header">
  <h1 class="page-title">页面标题</h1>
  <div class="page-actions">
    <button class="btn btn-primary">
      <i class="fas fa-plus"></i>
    </button>
  </div>
</div>

<!-- 搜索栏模板 -->
<div class="search-bar">
  <div class="search-input-wrapper">
    <i class="fas fa-search search-icon"></i>
    <input type="text" class="search-input" placeholder="搜索...">
  </div>
  <button class="search-filter-btn">
    <i class="fas fa-filter"></i>
  </button>
</div>

<!-- 加载状态模板 -->
<div class="loading-state">
  <div class="loading-spinner">
    <i class="fas fa-spinner fa-spin"></i>
  </div>
  <p class="loading-text">加载中...</p>
</div>

<!-- 空状态模板 -->
<div class="empty-state">
  <div class="empty-icon">
    <i class="fas fa-inbox"></i>
  </div>
  <h3 class="empty-title">暂无数据</h3>
  <p class="empty-description">当前没有可显示的内容</p>
  <button class="btn btn-primary">刷新</button>
</div>

<!-- 告警徽章模板 -->
<div class="alert-badge">
  <span class="badge badge-danger">3</span>
</div>

<!-- 状态指示器模板 -->
<div class="status-indicator">
  <span class="status-dot status-online"></span>
  <span class="status-text">在线</span>
</div>

<style>
/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light);
  margin-bottom: 16px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
}

.page-actions {
  display: flex;
  gap: 8px;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--border-light);
  border-radius: 8px;
  background: var(--card-white);
  font-size: 16px;
}

.search-filter-btn {
  padding: 12px;
  border: 1px solid var(--border-light);
  border-radius: 8px;
  background: var(--card-white);
  color: var(--text-secondary);
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  font-size: 32px;
  color: var(--primary-blue);
  margin-bottom: 16px;
}

.loading-text {
  color: var(--text-secondary);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 48px;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.empty-description {
  color: var(--text-secondary);
  margin-bottom: 24px;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  min-width: 16px;
  text-align: center;
}

.badge-danger {
  background: var(--danger-red);
}

.badge-warning {
  background: var(--warning-orange);
}

.badge-success {
  background: var(--success-green);
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-online {
  background: var(--success-green);
}

.status-offline {
  background: var(--text-secondary);
}

.status-warning {
  background: var(--warning-orange);
}

.status-error {
  background: var(--danger-red);
}

.status-text {
  font-size: 12px;
  color: var(--text-secondary);
}
</style>

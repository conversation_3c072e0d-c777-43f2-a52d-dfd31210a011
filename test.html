<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>UI测试页面 - 能源管理系统</title>
  <link rel="stylesheet" href="styles/common.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    body {
      padding: 20px;
      background: var(--background-gradient);
    }

    .test-result {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 20px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .success-icon {
      color: var(--success-green);
      font-size: 20px;
      margin-right: 8px;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .test-section {
      margin-bottom: 40px;
    }
    
    .test-title {
      font-size: 24px;
      font-weight: 700;
      color: white;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .component-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }
    
    .test-card {
      background: var(--card-white);
      border-radius: 16px;
      padding: 20px;
      box-shadow: var(--card-shadow);
      border: 1px solid var(--border-light);
      transition: all 0.3s ease;
    }
    
    .test-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--card-shadow-hover);
    }
    
    .color-palette {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;
    }
    
    .color-item {
      text-align: center;
      padding: 16px;
      border-radius: 12px;
      color: white;
      font-weight: 600;
      font-size: 12px;
    }
    
    .color-primary { background: var(--primary-blue); }
    .color-success { background: var(--success-green); }
    .color-warning { background: var(--warning-orange); }
    .color-danger { background: var(--danger-red); }
    
    .gradient-item {
      height: 80px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .gradient-primary { background: var(--primary-gradient); }
    .gradient-success { background: var(--success-gradient); }
    .gradient-warning { background: var(--warning-gradient); }
    .gradient-danger { background: var(--danger-gradient); }
    
    .button-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
    }
    
    .test-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .test-btn:hover {
      transform: translateY(-2px);
    }
    
    .btn-primary { background: var(--primary-blue); color: white; }
    .btn-success { background: var(--success-green); color: white; }
    .btn-warning { background: var(--warning-orange); color: white; }
    .btn-danger { background: var(--danger-red); color: white; }
    
    .metric-test {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }
    
    .metric-card-test {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 20px;
      border-radius: 16px;
      text-align: center;
    }
    
    .metric-value-test {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 8px;
    }
    
    .metric-label-test {
      font-size: 14px;
      opacity: 0.9;
    }
    
    .status-test {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }
    
    .status-dot-test {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
    
    .status-online-test { background: var(--success-green); }
    .status-warning-test { background: var(--warning-orange); }
    .status-offline-test { background: var(--text-secondary); }
    
    .glass-card {
      background: var(--glass-bg);
      backdrop-filter: var(--backdrop-blur);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      padding: 20px;
      color: white;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1 class="test-title">🎨 UI优化测试页面</h1>

    <!-- 测试结果总结 -->
    <div class="test-result">
      <h2 style="color: var(--text-primary); margin-bottom: 16px;">
        <i class="fas fa-check-circle success-icon"></i>
        优化完成测试结果
      </h2>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; color: var(--text-primary);">
        <div>
          <h4>✅ index.html 背景优化</h4>
          <p style="font-size: 14px; color: var(--text-secondary);">纯白背景 + 微妙渐变效果</p>
        </div>
        <div>
          <h4>✅ 时间选择器重设计</h4>
          <p style="font-size: 14px; color: var(--text-secondary);">现代化卡片式布局 + 图标</p>
        </div>
        <div>
          <h4>✅ 响应式布局优化</h4>
          <p style="font-size: 14px; color: var(--text-secondary);">完美适配手机端显示</p>
        </div>
        <div>
          <h4>✅ 交互效果增强</h4>
          <p style="font-size: 14px; color: var(--text-secondary);">悬停动画 + 点击反馈</p>
        </div>
      </div>
    </div>
    
    <!-- 色彩系统测试 -->
    <div class="test-section">
      <div class="test-card">
        <h3>色彩系统</h3>
        <div class="color-palette">
          <div class="color-item color-primary">主色调</div>
          <div class="color-item color-success">成功色</div>
          <div class="color-item color-warning">警告色</div>
          <div class="color-item color-danger">危险色</div>
        </div>
      </div>
    </div>
    
    <!-- 渐变色测试 -->
    <div class="test-section">
      <div class="test-card">
        <h3>渐变色系统</h3>
        <div class="color-palette">
          <div>
            <div class="gradient-item gradient-primary">主渐变</div>
            <p style="text-align: center; font-size: 12px;">Primary</p>
          </div>
          <div>
            <div class="gradient-item gradient-success">成功渐变</div>
            <p style="text-align: center; font-size: 12px;">Success</p>
          </div>
          <div>
            <div class="gradient-item gradient-warning">警告渐变</div>
            <p style="text-align: center; font-size: 12px;">Warning</p>
          </div>
          <div>
            <div class="gradient-item gradient-danger">危险渐变</div>
            <p style="text-align: center; font-size: 12px;">Danger</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 按钮组件测试 -->
    <div class="test-section">
      <div class="test-card">
        <h3>按钮组件</h3>
        <div class="button-grid">
          <button class="test-btn btn-primary">主要按钮</button>
          <button class="test-btn btn-success">成功按钮</button>
          <button class="test-btn btn-warning">警告按钮</button>
          <button class="test-btn btn-danger">危险按钮</button>
        </div>
      </div>
    </div>
    
    <!-- 指标卡片测试 -->
    <div class="test-section">
      <div class="test-card">
        <h3>指标卡片</h3>
        <div class="metric-test">
          <div class="metric-card-test">
            <div class="metric-value-test">1,245</div>
            <div class="metric-label-test">今日用电 (kWh)</div>
          </div>
          <div class="metric-card-test" style="background: linear-gradient(135deg, #11998e, #38ef7d);">
            <div class="metric-value-test">856</div>
            <div class="metric-label-test">今日用水 (m³)</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 状态指示器测试 -->
    <div class="test-section">
      <div class="test-card">
        <h3>状态指示器</h3>
        <div class="status-test">
          <span class="status-dot-test status-online-test"></span>
          <span>在线状态</span>
        </div>
        <div class="status-test">
          <span class="status-dot-test status-warning-test"></span>
          <span>警告状态</span>
        </div>
        <div class="status-test">
          <span class="status-dot-test status-offline-test"></span>
          <span>离线状态</span>
        </div>
      </div>
    </div>
    
    <!-- 毛玻璃效果测试 -->
    <div class="test-section">
      <div class="glass-card">
        <h3>毛玻璃效果卡片</h3>
        <p>这是一个使用毛玻璃效果的卡片组件，具有半透明背景和模糊效果。</p>
        <div style="display: flex; gap: 12px; margin-top: 16px;">
          <i class="fas fa-bolt" style="font-size: 24px;"></i>
          <i class="fas fa-tint" style="font-size: 24px;"></i>
          <i class="fas fa-fire" style="font-size: 24px;"></i>
          <i class="fas fa-leaf" style="font-size: 24px;"></i>
        </div>
      </div>
    </div>
    
    <!-- 阴影效果测试 -->
    <div class="test-section">
      <div class="component-grid">
        <div class="test-card" style="box-shadow: var(--card-shadow);">
          <h4>标准阴影</h4>
          <p>这是标准的卡片阴影效果</p>
        </div>
        <div class="test-card" style="box-shadow: var(--card-shadow-hover);">
          <h4>悬停阴影</h4>
          <p>这是悬停时的阴影效果</p>
        </div>
      </div>
    </div>
  </div>
</body>
</html>

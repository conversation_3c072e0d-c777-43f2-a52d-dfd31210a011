/* 能源管理APP通用样式 */
:root {
  --primary-blue: #007AFF;
  --success-green: #34C759;
  --warning-orange: #FF9500;
  --danger-red: #FF3B30;
  --background-gray: #F2F2F7;
  --card-white: #FFFFFF;
  --text-primary: #000000;
  --text-secondary: #8E8E93;
  --border-light: #E5E5EA;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-gray);
  color: var(--text-primary);
  line-height: 1.5;
}

/* iPhone 15 Pro 设备框架样式 */
.device-frame {
  width: 393px;
  height: 852px;
  background: #000;
  border-radius: 20px;
  padding: 2px;
  margin: 20px auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.device-screen {
  width: 100%;
  height: 100%;
  background: var(--background-gray);
  border-radius: 18px;
  overflow: hidden;
  position: relative;
}

/* iOS状态栏 */
.status-bar {
  height: 44px;
  background: var(--card-white);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 应用内容区域 */
.app-content {
  height: calc(100% - 44px - 83px);
  overflow-y: auto;
  padding: 16px;
}

/* 底部Tab栏 */
.tab-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 83px;
  background: var(--card-white);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding-top: 8px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  text-decoration: none;
  color: var(--text-secondary);
  transition: color 0.2s;
}

.tab-item.active {
  color: var(--primary-blue);
}

.tab-icon {
  font-size: 24px;
}

.tab-label {
  font-size: 10px;
  font-weight: 500;
}

/* 通用卡片样式 */
.card {
  background: var(--card-white);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-success {
  background: var(--success-green);
  color: white;
}

.btn-warning {
  background: var(--warning-orange);
  color: white;
}

.btn-danger {
  background: var(--danger-red);
  color: white;
}

/* 指标卡片 */
.metric-card {
  background: var(--card-white);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.metric-change {
  font-size: 12px;
  font-weight: 600;
  margin-top: 4px;
}

.metric-change.positive {
  color: var(--success-green);
}

.metric-change.negative {
  color: var(--danger-red);
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light);
}

.list-item:last-child {
  border-bottom: none;
}

.list-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.list-content {
  flex: 1;
}

.list-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.list-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

.list-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 16px;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-16 { margin-bottom: 16px; }
.mt-16 { margin-top: 16px; }
.p-16 { padding: 16px; }
.hidden { display: none; }
.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }

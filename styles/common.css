/* 能源管理APP通用样式 - 现代化设计系统 */
:root {
  /* 主色调 - 渐变蓝色系 */
  --primary-blue: #007AFF;
  --primary-blue-light: #5AC8FA;
  --primary-blue-dark: #0051D5;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-gradient-light: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* 功能色彩 */
  --success-green: #34C759;
  --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  --warning-orange: #FF9500;
  --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --danger-red: #FF3B30;
  --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);

  /* 中性色彩 */
  --background-gray: #F8F9FA;
  --background-gradient: #FFFFFF;
  --card-white: #FFFFFF;
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --card-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);

  /* 文字色彩 */
  --text-primary: #1A1A1A;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  --text-white: #FFFFFF;

  /* 边框色彩 */
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --border-dark: #9CA3AF;

  /* 特殊效果 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --backdrop-blur: blur(20px);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
  background: var(--background-gradient);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 16px;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* iPhone 15 Pro 设备框架样式 - 增强版 */
.device-frame {
  width: 393px;
  height: 852px;
  background: linear-gradient(145deg, #2c2c2e, #1c1c1e);
  border-radius: 24px;
  padding: 3px;
  margin: 20px auto;
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 10px 20px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.device-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 30px;
  background: linear-gradient(145deg, #2c2c2e, #1c1c1e);
  border-radius: 0 0 20px 20px;
  z-index: 10;
}

.device-frame::after {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 6px;
  background: #1c1c1e;
  border-radius: 3px;
  z-index: 11;
}

.device-screen {
  width: 100%;
  height: 100%;
  background: var(--background-gray);
  border-radius: 21px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

/* iOS状态栏 */
.status-bar {
  height: 44px;
  background: var(--card-white);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 应用内容区域 */
.app-content {
  height: calc(100% - 44px - 83px);
  overflow-y: auto;
  padding: 16px;
}

/* 底部Tab栏 */
.tab-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 83px;
  background: var(--card-white);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding-top: 8px;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  text-decoration: none;
  color: var(--text-secondary);
  transition: color 0.2s;
}

.tab-item.active {
  color: var(--primary-blue);
}

.tab-icon {
  font-size: 24px;
}

.tab-label {
  font-size: 10px;
  font-weight: 500;
}

/* 通用卡片样式 - 现代化设计 */
.card {
  background: var(--card-white);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}

.card-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-success {
  background: var(--success-green);
  color: white;
}

.btn-warning {
  background: var(--warning-orange);
  color: white;
}

.btn-danger {
  background: var(--danger-red);
  color: white;
}

/* 指标卡片 */
.metric-card {
  background: var(--card-white);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.metric-change {
  font-size: 12px;
  font-weight: 600;
  margin-top: 4px;
}

.metric-change.positive {
  color: var(--success-green);
}

.metric-change.negative {
  color: var(--danger-red);
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light);
}

.list-item:last-child {
  border-bottom: none;
}

.list-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: white;
}

.list-content {
  flex: 1;
}

.list-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.list-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

.list-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 16px;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-16 { margin-bottom: 16px; }
.mt-16 { margin-top: 16px; }
.p-16 { padding: 16px; }
.hidden { display: none; }
.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
